#!/bin/bash
set -eo pipefail

# Environment variables expected to be set externally or by Helm
ENV="${ENVIRONMENT}"
SERVICE_NAME="${SERVICE_NAME}"
SECRET_NAME="${SERVICE_POSTGRES_SECRET_NAME}"
NAMESPACE="${NAMESPACE}"
ENV_TYPE="${ENV_TYPE}"

# Check if the Kubernetes Secret already exists
secret_exists() {
    echo "Checking if Kubernetes secret '$1' exists in namespace $NAMESPACE..."
    if kubectl get secret "$1" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "Secret '$1' exists."
        return 0
    else
        echo "Secret '$1' not found."
        return 1
    fi
}

# Generate a secure 16-character alphanumeric password
generate_password() {
    openssl rand -base64 32 | tr -dc 'a-zA-Z0-9' | head -c16
}

# Create the Secret in Kubernetes
create_postgres_secret() {
    echo "Creating PostgreSQL secret..."
    local password
    password=$(generate_password)

    # Build the config string like <PERSON><PERSON> does
    local config_content
    config_content="${ENV_TYPE}.${ENV}.db.default.password = ${password}
${ENV_TYPE}.${ENV}.db.default.properties.password = ${password}"

    # Create Kubernetes Secret with labels & annotations (manually add them since Helm doesn’t run this)
    kubectl create secret generic "$SECRET_NAME" \
        --namespace "$NAMESPACE" \
        --from-literal=password="$password" \
        --from-literal=postgres-access-override.conf="$config_content" \
        --dry-run=client -o yaml | \
    kubectl apply -f -

   

    
}

main() {
    if ! secret_exists "$SECRET_NAME"; then
        create_postgres_secret
    else
        echo "Secret $SECRET_NAME already exists - skipping creation."
    fi
}

main
