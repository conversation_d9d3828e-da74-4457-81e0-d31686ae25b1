#!/bin/bash
set -eo pipefail

# Environment variables from Helm
ENV="${ENVIRONMENT}"
SERVICE_NAME="${SERVICE_NAME}"
SECRET_NAME="${SERVICE_MONGO_SECRET_NAME}"
NAMESPACE="${NAMESPACE}"
ENV_TYPE="${ENV_TYPE}"
MONGO_URI="${MONGO_HOST}"

# Defaults (used if not overridden by env)
AUTH_DB="${MONGO_AUTHENTICATION_DATABASE:-NSP_Users}"
DB_TO_CONNECT="${MONGO_DB_TO_CONNECT:-NSP_Users}"

secret_exists() {
    echo "Checking if Kubernetes secret '$1' exists in namespace $NAMESPACE..."
    if kube<PERSON>l get secret "$1" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "Secret '$1' exists."
        return 0
    else
        echo "Secret '$1' not found."
        return 1
    fi
}

generate_password() {
    openssl rand -base64 16 | tr -dc 'a-zA-Z0-9' | head -c16
}

generate_mongo_override_conf() {
    cat <<EOF
${ENV_TYPE}.${ENV}.apps.db.mongo.url = "${MONGO_URI}"
apps.db.mongo.url = "${MONGO_URI}"
${ENV_TYPE}.${ENV}.mongodb.uri = "${MONGO_URI}"
mongodb.uri = "${MONGO_URI}"
EOF
}

create_mongo_secret() {
    echo "Creating MongoDB secret..."
    local password
    password=$(generate_password)

    local override_conf
    override_conf=$(generate_mongo_override_conf)

    kubectl create secret generic "$SECRET_NAME" \
        --namespace "$NAMESPACE" \
        --from-literal=password="$password" \
        --from-literal=mongo-access-override.conf="$override_conf" \
        --dry-run=client \
        -o yaml | kubectl apply -f -

}

main() {
    if ! secret_exists "$SECRET_NAME"; then
        create_mongo_secret
    else
        echo "Secret $SECRET_NAME already exists - skipping creation"
    fi
}

main
