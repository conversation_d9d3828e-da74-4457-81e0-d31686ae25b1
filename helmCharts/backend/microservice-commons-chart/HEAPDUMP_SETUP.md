# Heapdump Management Setup

This document describes the shared heapdump management infrastructure provided by the microservice-commons-chart.

## Overview

The heapdump management system provides:
- **Shared EFS volume** for storing heap dumps from all microservices
- **Automated S3 upload** and cleanup via CronJob
- **IRSA-based authentication** for secure S3 access
- **Pod-specific directories** to organize heap dumps by service instance

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Service A     │    │   Service B     │    │   Service C     │
│   Pod-0         │    │   Pod-0         │    │   Pod-0         │
│                 │    │                 │    │                 │
│ /opt/heapdumps/ │    │ /opt/heapdumps/ │    │ /opt/heapdumps/ │
│   └─ ${POD_NAME}│    │   └─ ${POD_NAME}│    │   └─ ${POD_NAME}│
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Shared EFS Volume     │
                    │   kalpha-heapdump-pvc     │
                    │                           │
                    │ /service-a-pod-0/         │
                    │ /service-b-pod-0/         │
                    │ /service-c-pod-0/         │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────▼─────────────┐
                    │   Cleanup CronJob         │
                    │   (Every 15 minutes)      │
                    │                           │
                    │ 1. Scan for .hprof files  │
                    │ 2. Upload to S3           │
                    │ 3. Verify upload          │
                    │ 4. Delete local files     │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────▼─────────────┐
                    │         S3 Bucket         │
                    │  stage-k8s-heapdumps      │
                    │                           │
                    │ /service-a-pod-0/         │
                    │ /service-b-pod-0/         │
                    │ /service-c-pod-0/         │
                    └───────────────────────────┘
```

## Components

### 1. Shared Infrastructure (microservice-commons-chart)
- **PersistentVolume**: `{environment}-heapdump-pv`
- **PersistentVolumeClaim**: `{environment}-heapdump-pvc`
- **ServiceAccount**: `{environment}-heapdump-cleanup` (with IRSA)
- **RBAC**: Role and RoleBinding for cleanup job
- **CronJob**: `{environment}-heapdump-cleanup`

### 2. Service Integration (backend-service-chart)
- **Volume Mount**: Mounts shared PVC at `/opt/heapdumps`
- **JVM Configuration**: Heap dumps go to `/opt/heapdumps/${POD_NAME}`
- **Directory Creation**: Creates pod-specific subdirectories

## Setup Instructions

### 1. Deploy Commons Chart
```bash
# Deploy the microservice-commons-chart first
helm upgrade --install microservice-commons helmCharts/backend/microservice-commons-chart \
  -f helmCharts/backend/microservice-commons-chart/values/commons-values.yaml \
  --namespace stage-backend
```

### 2. Run AWS Setup Script
```bash
# Create S3 bucket, IAM role, and OIDC trust relationship
cd helmCharts/backend/microservice-commons-chart
./setup-heapdump-s3.sh
```

### 3. Deploy Services
```bash
# Deploy individual services with heapdump.enabled: true
helm upgrade --install n-authentication helmCharts/backend/backend-service-chart \
  -f helmCharts/backend/backend-service-chart/values/kalpha/n-authentication/values.yaml \
  -f helmCharts/backend/backend-service-chart/values/kalpha/n-authentication/image.yaml \
  --namespace stage-backend
```

## Configuration

### Commons Chart Values (commons-values.yaml)
```yaml
heapdump:
  enabled: true
  storage: "10Gi"
  storageClassName: "efs-sc"
  efsFileSystemId: "fs-0d322c38acb78fddb"
  efsPath: "/"
  s3Bucket: "stage-k8s-heapdumps"
  s3Region: "ap-southeast-1"
  serviceAccount:
    roleArn: "arn:aws:iam::************:role/EKS-HeapdumpCleanup-Role"
  cleanup:
    schedule: "*/15 * * * *"
    image: "amazon/aws-cli:2.13.25"
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"
```

### Service Chart Values (image.yaml)
```yaml
heapdump:
  enabled: true  # Mount the shared heapdump volume
```

## Monitoring

### Check CronJob Status
```bash
kubectl get cronjob kalpha-heapdump-cleanup -n stage-backend
kubectl get jobs -l component=heapdump-cleanup -n stage-backend
```

### View Cleanup Logs
```bash
kubectl logs -l component=heapdump-cleanup -n stage-backend --tail=100
```

### Check S3 Uploads
```bash
aws s3 ls s3://stage-k8s-heapdumps/ --recursive
```

### Verify Volume Mounts
```bash
kubectl exec -it <pod-name> -n stage-backend -- ls -la /opt/heapdumps/
```

## Troubleshooting

### Common Issues

1. **Volume Mount Failures**
   - Check EFS file system ID and mount targets
   - Verify security groups allow NFS traffic (port 2049)
   - Ensure EFS CSI driver is installed

2. **S3 Upload Failures**
   - Verify IAM role trust relationship
   - Check ServiceAccount annotations
   - Ensure OIDC provider is configured

3. **Permission Errors**
   - Check RBAC permissions for cleanup job
   - Verify pod security contexts

### Manual Cleanup
```bash
# Manually trigger cleanup job
kubectl create job --from=cronjob/kalpha-heapdump-cleanup manual-cleanup-$(date +%s) -n stage-backend
```

## Security

- **IRSA**: Uses IAM Roles for Service Accounts for secure S3 access
- **Least Privilege**: Cleanup job has minimal required permissions
- **Network Security**: EFS access controlled via security groups
- **Encryption**: EFS volume is encrypted at rest
