# Sets the environment
environment: "kalpha"

#REQUIRED
# prod/stage # Used for creating the service conf
envType: "stage"

# Heapdump EFS volume configuration (shared across all microservices)
heapdump:
  enabled: true
  storage: "10Gi"  # Larger storage for shared volume
  storageClassName: "efs-sc"
  efsFileSystemId: "fs-0d322c38acb78fddb"
  efsPath: "/"
  # S3 configuration for heapdump cleanup job
  s3Bucket: "stage-k8s-heapdumps"
  s3Region: "ap-southeast-1"
  # ServiceAccount configuration for heapdump cleanup job
  serviceAccount:
    roleArn: "arn:aws:iam::************:role/EKS-HeapdumpCleanup-Role"
    annotations: {}
  # CronJob configuration
  cleanup:
    schedule: "*/15 * * * *"  # Every 15 minutes
    image: "amazon/aws-cli:2.13.25"
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"

hostnameSuffix: ".strawmine.com"
# List of service names that will dynamically populate serviceEndpointsConfigMap (override-service-endpoints).
# For now generating through:
# cat nsg1-n-mini-store-integration-override.conf | grep -E "service-url=|service-admin-external-url" | cut -d "=" -f1 | sed 's/prod.nsg1.//g' | sed 's/-service-url//g' | sed 's/-service-admin-external-url//g' | sort -d  | uniq | xargs  | sed 's/ /", "/g' | sed 's/^/"/'| sed 's/$/"/'
dependentServiceNames:
  - categories
  - n-api-gateway
  - n-authentication
  - n-business-admin-web
  - n-core-cate-data-mediator
  - n-data-integrations
  - n-data-seeding
  - n-device-registration
  - n-event-hub
  - n-fabric-inspection
  - n-factory-config-backend
  - n-factory-configuration-admin
  - n-flink-provision
  - n-generic-app
  - n-input-app-web
  - n-live-data-streaming
  - n-machine-maintenance
  - n-machine-maintenance-web
  - n-mini-store-integration
  - n-notifications
  - n-oneapp-web
  - n-order-api-gateway
  - n-order-service
  - n-order-validation
  - n-org-hierarchy
  - n-packing-tool
  - n-periodic-redis-sync
  - n-planning-tool
  - n-planning-tool-reports
  - n-planning-tool-web
  - n-remote-deployment-web
  - n-reports-backend
  - n-reports-oi-web
  - n-reports-web
  - n-silhouettes-management
  - n-tags-management
  - n-workflow-dashboard-web
  - n-workflow-data-integrations
  - n-workflow-reports-backend
  - n-workflow-reports-web
  - n-workflow-services
  - n-workflow-web
  - n-workshift-management
  - unified-id

# Map of external service names to their URL suffixes or full hostnames
externalServiceUrls:
  n-api-gateway: "n-api-gateway-external"
  n-business-admin-web: "factoryadmin.kalpha"
  n-core-cate-data-mediator: "n-core-cate-data-mediator-external"
  n-factory-configuration-admin: "n-factory-configuration-admin-external"
  n-input-app-web: "n-input-app-web-external"
  n-live-data-streaming: "service-external"
  n-machine-maintenance-web: "n-machine-maintenance-web-external"
  n-oneapp-web: "oneapp.kalpha"
  n-planning-tool-web: "n-planning-tool-web-external"
  n-reports-web: "analytics.kalpha"
  
externalServiceNames:
  - n-api-gateway
  - n-business-admin-web
  - n-core-cate-data-mediator
  - n-factory-configuration-admin
  - n-input-app-web
  - n-live-data-streaming
  - n-machine-maintenance-web
  - n-oneapp-web
  - n-planning-tool-web
  - n-reports-web


# dependentServiceNames: ["categories", "n-api-gateway", "n-authentication", "n-business-admin-web", "n-core-cate-data-mediator", "n-data-integrations", "n-data-seeding", "n-device-registration", "n-event-hub", "n-fabric-inspection", "n-factory-config-backend", "n-factory-configuration-admin", "n-flink-provision", "n-generic-app", "n-input-app-web", "n-live-data-streaming", "n-machine-maintenance", "n-machine-maintenance-web", "n-mini-store-integration", "n-notifications", "n-oneapp-web", "n-order-api-gateway", "n-order-service", "n-order-validation", "n-org-hierarchy", "n-packing-tool", "n-periodic-redis-sync", "n-planning-tool", "n-planning-tool-reports", "n-planning-tool-web", "n-remote-deployment-web", "n-reports-backend", "n-reports-oi-web", "n-reports-web", "n-silhouettes-management", "n-tags-management", "n-workflow-dashboard-web", "n-workflow-data-integrations", "n-workflow-reports-backend", "n-workflow-reports-web", "n-workflow-services", "n-workflow-web", "n-workshift-management", "service-url", "unified-id"]

# # Default values for microservice-commons-chart.
# # This is a YAML-formatted file.
# # Declare variables to be passed into your templates.

# # This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
# replicaCount: 1

# # This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
# image:
#   repository: nginx
#   # This sets the pull policy for images.
#   pullPolicy: IfNotPresent
#   # Overrides the image tag whose default is the chart appVersion.
#   tag: ""

# # This is for the secretes for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
# imagePullSecrets: []
# # This is to override the chart name.
# nameOverride: ""
# fullnameOverride: ""

# #This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
# serviceAccount:
#   # Specifies whether a service account should be created
#   create: true
#   # Automatically mount a ServiceAccount's API credentials?
#   automount: true
#   # Annotations to add to the service account
#   annotations: {}
#   # The name of the service account to use.
#   # If not set and create is true, a name is generated using the fullname template
#   name: ""

# # This is for setting Kubernetes Annotations to a Pod.
# # For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/ 
# podAnnotations: {}
# # This is for setting Kubernetes Labels to a Pod.
# # For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
# podLabels: {}

# podSecurityContext: {}
#   # fsGroup: 2000

# securityContext: {}
#   # capabilities:
#   #   drop:
#   #   - ALL
#   # readOnlyRootFilesystem: true
#   # runAsNonRoot: true
#   # runAsUser: 1000

# # This is for setting up a service more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/
# service:
#   # This sets the service type more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types
#   type: ClusterIP
#   # This sets the ports more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/#field-spec-ports
#   port: 80

# # This block is for setting up the ingress for more information can be found here: https://kubernetes.io/docs/concepts/services-networking/ingress/
# ingress:
#   enabled: false
#   className: ""
#   annotations: {}
#     # kubernetes.io/ingress.class: nginx
#     # kubernetes.io/tls-acme: "true"
#   hosts:
#     - host: chart-example.local
#       paths:
#         - path: /
#           pathType: ImplementationSpecific
#   tls: []
#   #  - secretName: chart-example-tls
#   #    hosts:
#   #      - chart-example.local

# resources: {}
#   # We usually recommend not to specify default resources and to leave this as a conscious
#   # choice for the user. This also increases chances charts run on environments with little
#   # resources, such as Minikube. If you do want to specify resources, uncomment the following
#   # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
#   # limits:
#   #   cpu: 100m
#   #   memory: 128Mi
#   # requests:
#   #   cpu: 100m
#   #   memory: 128Mi

# # This is to setup the liveness and readiness probes more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
# livenessProbe:
#   httpGet:
#     path: /
#     port: http
# readinessProbe:
#   httpGet:
#     path: /
#     port: http

# #This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
# autoscaling:
#   enabled: false
#   minReplicas: 1
#   maxReplicas: 100
#   targetCPUUtilizationPercentage: 80
#   # targetMemoryUtilizationPercentage: 80

# # Additional volumes on the output Deployment definition.
# volumes: []
# # - name: foo
# #   secret:
# #     secretName: mysecret
# #     optional: false

# # Additional volumeMounts on the output Deployment definition.
# volumeMounts: []
# # - name: foo
# #   mountPath: "/etc/foo"
# #   readOnly: true

# nodeSelector: {}

# tolerations: []

# affinity: {}
