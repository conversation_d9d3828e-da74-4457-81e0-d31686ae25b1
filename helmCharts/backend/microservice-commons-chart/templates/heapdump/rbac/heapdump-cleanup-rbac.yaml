{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $environment }}-heapdump-cleanup
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
rules:
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $environment }}-heapdump-cleanup
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
subjects:
- kind: ServiceAccount
  name: {{ $environment }}-heapdump-cleanup
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: {{ $environment }}-heapdump-cleanup
  apiGroup: rbac.authorization.k8s.io
{{- end }}
