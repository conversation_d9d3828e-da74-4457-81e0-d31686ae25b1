{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $environment }}-heapdump-cleanup
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
  annotations:
    {{- if .Values.heapdump.serviceAccount.roleArn }}
    eks.amazonaws.com/role-arn: {{ .Values.heapdump.serviceAccount.roleArn }}
    {{- end }}
    {{- with .Values.heapdump.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
automountServiceAccountToken: true
{{- end }}
