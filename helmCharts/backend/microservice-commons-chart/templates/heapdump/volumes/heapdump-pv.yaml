{{- if and .Values.heapdump .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ $environment }}-heapdump-pv
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
    component: heapdump-storage
spec:
  capacity:
    storage: {{ .Values.heapdump.storage | default "5Gi" }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ .Values.heapdump.storageClassName | default "efs-sc" }}
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ .Values.heapdump.efsFileSystemId | quote }}
    volumeAttributes:
      path: {{ .Values.heapdump.efsPath | default "/" | quote }}
{{- end }}