{{- if and .Values.heapdump .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ $environment }}-heapdump-pvc
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
    component: heapdump-storage
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.heapdump.storage | default "10Gi" }}
  storageClassName: {{ .Values.heapdump.storageClassName | default "efs-sc" }}
  volumeName: {{ $environment }}-heapdump-pv
{{- end }}
