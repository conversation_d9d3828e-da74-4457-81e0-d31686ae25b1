{{- range $frontendService := .Values.frontendServices }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ $frontendService }}
  namespace: argo
spec:
  destination:
    namespace: stage-backend
    server: https://kubernetes.default.svc
  project: default
  source:
    repoURL: *****************:ncinga/backend-k8s.git
    targetRevision: master
    path: helmCharts/backend/frontend-service-chart
    helm:
      valueFiles:
        - values/{{ $.Values.env }}/{{ $frontendService }}/values.yaml
        - values/{{ $.Values.env }}/{{ $frontendService }}/image.yaml
  syncPolicy:
    syncOptions:
      - PruneLast=true
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - ServerSideApply=true
---
{{- end }}
