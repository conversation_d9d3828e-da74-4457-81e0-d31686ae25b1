{{- range $backendService := .Values.backendServices }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ $backendService }}
  namespace: argo
spec:
  destination:
    namespace: stage-backend
    server: https://kubernetes.default.svc
  project: default
  source:
    repoURL: *****************:ncinga/backend-k8s.git
    targetRevision: master
    path: helmCharts/backend/backend-service-chart
    helm:
      valueFiles:
        - values/{{ $.Values.env }}/{{ $backendService }}/values.yaml
        - values/{{ $.Values.env }}/{{ $backendService }}/image.yaml
  syncPolicy:
    syncOptions:
      - PruneLast=true
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - ServerSideApply=true
---
{{- end }}
