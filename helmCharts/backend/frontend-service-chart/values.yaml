# Overriden by namespace provided in helm install
namespace: kalpha-stage-backend

# For devops: set to true for debugging the init job 
scriptDebuggingEnabled: True


#REQUIRED
# Sets the service name
serviceName: "n-machine-maintenance-web" 


#REQUIRED
# Sets the environment
environment: "kalpha" 

#REQUIRED
# prod/stage # Used for creating the service conf
envType: "stage" 

replicaCount: 1
image:
  # repository: 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/kalpha-machine-maintenance-web
  # tag: "v4"
  repository: 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/machine-maintenance-web
  tag: "e85a4c27328eacd394a25057cea1434560f988a2"

  # 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/machine-maintenance-web:k-alpha
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.

hostnameSuffix: ".strawmine.com" # Default is .strawmine.com


ingress:
  className: "external-nginx"
  blocks: 
    - name: "root"
      location: "/"
      additionalAnnotations: {}
    # - name: "websocket"
    #   location: "/wsapi"
    #   # Any location specific annotations can be added here: eg: body size, or
    #   additionalAnnotations: {}
  client_endpoint:
    - name: "machine-maintenance-web" # This is irrelevant but is used for readability
      host: "kalpha-n-machine-maintenance-external.strawmine.com"
      serviceIdentifier: "" # without / prefix
      annotations: {}
    # - name: "n-live-data-streaming-web" # This is irrelevant but is used for readability
    #   host: "kalpha-n-service-external.strawmine.com"
    #   serviceIdentifier: "maintain-backend"
    #   ext_enabled: false
    #   annotations: {}
  # admin_endpoint:
  #   - name: "mm admin endpoint"
  #     host: "kalpha-machine-maintenance.admin.strawmine.com"
  #     annotations:
  #       nginx.ingress.kubernetes.io/auth-signin: https://oauth2-solvei8.strawmine.com/oauth2/start?rd=/redirect/$http_host$escaped_request_uri
  #       nginx.ingress.kubernetes.io/auth-url: https://oauth2-solvei8.strawmine.com/oauth2/auth

service:
  type: ClusterIP
  port: 80
  targetPort: 80


livenessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

# Readiness probe configuration
readinessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 10
  periodSeconds: 15
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3


# Resources for the frontend nignx pod serving assets
resources: {}
# resources:
#   limits:
#     cpu: "500m"
#     memory: "512Mi"
#   requests:
#     cpu: "250m"
#     memory: "256Mi"


volumeMounts: []
# # - name: foo
# #   mountPath: "/etc/foo"
# #   readOnly: true


serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""