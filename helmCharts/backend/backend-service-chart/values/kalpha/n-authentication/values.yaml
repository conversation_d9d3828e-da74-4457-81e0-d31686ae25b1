#REQUIRED
# Sets the service name
serviceName: "n-authentication"

# For devops: set to true for debugging the init job 
scriptDebuggingEnabled: True

#REQUIRED
# Sets the environment
environment: "kalpha" 

#REQUIRED
# prod/stage # Used for creating the service conf
envType: "stage" 

# REQUIRED
# can be prod.conf or prod_conf.conf for few services, check chef before deploy
configurationFile: "/app/conf/application.conf"

# This is used for configuration value multienv.hostname_suffix and multienv.admin_hostname_suffix (Not sure if this is actively used)
hostnameSuffix: ".strawmine.com" # Default is .strawmine.com

hostAliases:
  - ip: "************"
    hostnames:
      - "alpha-unified-id.strawmine.com"


overrideServiceEndpoints: 
  # stage.kalpha.unified-id-service-url: "http://nsg1-n-device-registration.ncinga.com"
  stage.kalpha.userside-web-id-external-host-url: "alpha-id.strawmine.com"
  # stage.kalpha.userside-web-id-external-host-url: "alpha-id.strawmine.com"
  stage.kalpha.unified-id-service-url: "http://alpha-unified-id.strawmine.com"
  stage.kalpha.unified-id-service-admin-external-url: "https://alpha-unified-id.strawmine.com"
  stage.kalpha.categories-service-url: "http://alpha-categories.strawmine.com"
  stage.kalpha.categories-service-admin-external-url: "https://alpha-categories.strawmine.com"
  stage.kalpha.n-live-data-streaming-external-host-url: "https:/kalpha-service-external.strawmine.com"

# TODO: add service with endpoint to unified-id internal.


# These values will be mounted as such in override-conf
# Example:
# overrideServiceConfs:
#     stage.kalpha.s3-bucket.name: "test-bucket"
#     stage.kalpha.s3-bucket.region: "ap-southeast-1a"

overrideServiceConfs: {}
overrideServiceConfsWithoutQuotes: {}
overrideServiceConfSecret:
    name: 
    key: 

postgres:
  enabled: true       # Required if `enabled` is true
  username: ""  # Defaults to <qualifiedServiceName> replacing hyphen with underscore
  # password: ""  # Generated by helm and stored in secret
  dbName: ""    # Defaults to the postgres.username
  port: "5432" # Port at which the service listens to. Should be same as the port where the postgres is running
  cluster: "stage-nint-new.cyiz7opktgoc.ap-southeast-1.rds.amazonaws.com"
  dbConnectionpools:
    - name: "default"
      maxConnections: "10"
      minConnections: "5"
      numThreads: "10"
      connectionTimeout: "4000"
      registerMbeans: true


mongo:
  enabled: true
  mongoUri: "mongodb://************:27017,***********:27017,***********:27017/?replicaSet=eks-mongo-3"
  external:
    enabled: true  # Set to true for using an external PostgreSQL
    ip: "************"           # Required if `enabled` is true
    port: "27017"
  username: ""  # Defaults to <environment>-<serviceName> replacing hyphen with underscore
  # password: "" # To be generated
  authenticationDatabase: "admin" # Defaults to NSP_Users. This is the database where your service user will be created.
  dbToConnect: "admin" # Defaults to NSP_Users. This is the database where the mongo will connect.
  cluster: "************:27017,***********:27017,***********:27017" # Postgres service endpoint
  # TODO: store replicasetName name in  db-admin
  replicasetName: "eks-mongo-3" 
  port: "27017" # This is the service port.
  dbRoles:
    - role: "readWrite"
      db: "orders"
    - role: "read"
      db: "sales"


kafka:
  enabled: true
  restEnabled: true
  serviceName: "stage-nalpha-kafka"
  namespace: "stage-kafka"
  # The service will have endpoint of masterEndpoint:port and masterEndpoint:restPort.
  port: 9093
  restPort: 8080
  zookeeperPort: 2181
  masterEndpoint: "************"
  producer:
    acks: -1    #Adding Acknowledgement from all insync replicas , to ensure reliability
    producerType: "sync"     # specifies whether the messages are sent asynchronously (async) or synchronously (sync)
    messageMaxBytes: "10485760"
    maxRequestSize: "10485760"
  consumer:
    maxPollIntervalMs: "864000000" # Defaults to 10 days
    messageMaxBytes: "10485760"
  # List of broker IP addresses. This will be used in kafka producer configuration.
  # broker_endpoints: []
  brokerEndpoints:
    - "************"
    - "***********"
    - "***********"



analyticKafka:
  enabled: true
  restEnabled: true
  serviceName: "stage-nalpha-analytic-kafka-rest"
  namespace: "stage-analytic-kafka"  # Namespace for analytic-kafka service
  # The service will have endpoint of masterEndpoint:port and masterEndpoint:restPort.
  port: 9093
  restPort: 8090
  zookeeperPort: 2182
  masterEndpoint: "************"
  # List of broker IP addresses. This will be used in kafka producer configuration.
  # broker_endpoints: []
  brokerEndpoints:
    - "************"
    - "***********"
    - "***********"
  # brokerEndpoints:
  #   - "*************"
  #   - "*************"




ingress:
  className: "external-nginx"
  http_context:
    context: "blz/nsp_auth"
    enabled: false
  blocks: 
    - name: "root"
      location: "/"
      additionalAnnotations:
        nginx.ingress.kubernetes.io/proxy-connect-timeout: "3"
        nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/client-max-body-size: "50m"
        
    - name: "admin"
      location: "/admin"
      additionalAnnotations:
        nginx.ingress.kubernetes.io/proxy-connect-timeout: "3"
        nginx.ingress.kubernetes.io/proxy-next-upstream: "error"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/client-max-body-size: "50m"

  client_endpoint: 
    - name: "n-machine-maintenance-web" 
      host: "kalpha-n-machine-maintenance-web-external.strawmine.com"  # Changed hostname
      serviceIdentifier: "auth_services"
      ext_enabled: true
      annotations: {}
    - name: "n-live-data-streaming" 
      host: "kalpha-service-external.strawmine.com"  # Changed hostname
      serviceIdentifier: "auth_services"
      ext_enabled: false
      annotations: {}
  admin_endpoint: 
    - name: "n-authentication-endpoint"
      host: "kalpha-n-authentication.admin.strawmine.com"  # Changed hostname
      annotations: {}
        # nginx.ingress.kubernetes.io/auth-signin: https://oauth2-solvei8.strawmine.com/oauth2/start?rd=/redirect/$http_host$escaped_request_uri
        # nginx.ingress.kubernetes.io/auth-url: https://oauth2-solvei8.strawmine.com/oauth2/auth


s3:
  enabled: false
  s3ObjectStorageInterfaceFactoryModuleEnabled: false
  region: "" # Defaults to ap-southeast-1
  access: #List of all the buckets for which access is required.
    - bucketName: solvei8-stage-kalpha-n-machine-maintenance
      acl: write # it can be read or write


initJob:
  enabled: true
  serviceAccount: backend-service-init-job-sa



replicaCount: 2
image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/zilingo-authentication-backend
#  tag: "4be4ee227561e39bf2a42a6e3d52020b097599a9"
  tag: "1661371"
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  ports:
    - name: http
      port: 80
      targetPort: 9000
      protocol: TCP
    - name: jmx-port
      port: 9050
      targetPort: 9050
      protocol: TCP
  port: 80
  targetPort: 9000


# Liveness probe configuration
livenessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

# Readiness probe configuration
readinessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 10
  periodSeconds: 15
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

# Resources for the backend pod
# resources: {}
resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "250m"
    memory: "256Mi"

java:
  agents:
    # - name: "jmx_prometheus_javaagent"
    #   path: "/opt/javaagents/jmx_prometheus_javaagent-0.20.0.jar=9050:/opt/service-confs/jmx_prometheus_config.yaml"
    #   options: ""
    - name: "jmx_prometheus_javaagent"
      path: "/opt/javaagents/jmx_prometheus_javaagent-0.20.0.jar=9050:/opt/service-confs/jmx_prometheus_config.yaml"
      options: ""

    - name: "opentelemetry_javaagent"
      path: "/opt/javaagents/opentelemetry-javaagent.jar"
      options: "-Dotel.resource.attributes=qualified.service.name=${QUALIFIED_SERVICE_NAME},host.name=$POD_NAME -Dotel.javaagent.debug=false -Dotel.exporter.otlp.protocol=grpc -Dotel.exporter.otlp.endpoint=http://alloy.observability-alloy.svc.cluster.local:4317 -Dotel.traces.exporter=otlp -Dotel.logs.exporter=otlp -Dotel.metric.export.interval=15000 -Dotel.metrics.exporter=otlp -Dotel.service.name=${QUALIFIED_SERVICE_NAME}"
  
  additionalOpts: ""

  # additionalOpts: "-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=*:5005"

# Application-specific arguments (APP_ARGS)
app:
  # logDir: "/var/log/n-machine-maintenance"
  pidfilePath: "/dev/null"
  configFile: "/opt/service-confs/override.conf" # This is the location to put all the override configs. This will include the absolute paths of other configs.
  # loggerFile: "${logger_file}"
  # serviceInstanceRole: "${SERVICE_INSTANCE_ROLE}"
  serviceInstanceNodeId: "9999" # Not sure if this is used by backend service
  statusReportStartedBy: "ops-user" # TODO: Set this up with argoCD pipeline.
  javaTmpDir: "/tmp/n-authentication"
# TODO: Add heapdumps to s3 in service init.
# Whenever the service starts, it should check if there is any file in /opt/heapdumps/
# /opt/heapdumps/ is a volume attached to each pod.
# init job should send this to s3, and clear the volume.
# Volume can be of size 2G
# 
# JVM-specific arguments (JVM_ARGS)
jvm:
  heapDumpPath: "/opt/heapdumps"
# JVM memory settings (JVM_MEMORY_ARGS)
  memory:
    Xms: "256m"
    Xmx: "256m"

# # Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# # - name: foo
# #   mountPath: "/etc/foo"
# #   readOnly: true

multiDCServiceUnified: false


# Some additional configurations required by backend services. These configs don't change so they will be part of default.
# This will make clusterIP service type with load balancing for connecting to external cluster
druid:
  namespace: "stage-druid-cluster"  # Namespace for druid service
  coordinator:
    enabled: false
    port: 8081
    serviceName: "stage-nalpha-druid-coordinator"
    endpoints: [ "************" ]
  broker:
    enabled: false
    port: 8082
    serviceName: "stage-nalpha-druid-broker"
    endpoints: [ "***********" ]

# TODO add these to override-conf

elasticsearch:
  enabled: false # if it is required by the service or not
  namespace: "stage-elasticsearch-cluster"  # Namespace for Elasticsearch service
  serviceName: "elasticsearch-3s"
  port: 9200
  external: 
    enabled: true # true or false
    endpoints:
      - "************"
      - "***********"
      - "***********" # If we are using ES on VM, we want the IP address and port for them
    IPs:
      - "************"
      - "***********"
      - "***********"

# For k8s based ES: 
# prod.nsg1.apps.db.elasticsearch.node1 =  clusterIPservicename.ns.svc.cluster.local:PORT
# prod.nsg1.apps.db.elasticsearch.node2 =  clusterIPservicename.ns.svc.cluster.local:PORT
# prod.nsg1.apps.db.elasticsearch.node3 =  clusterIPservicename.ns.svc.cluster.local:PORT

# For external based ES:
# prod.nsg1.apps.db.elasticsearch.node1 =  IP1:PORT
# prod.nsg1.apps.db.elasticsearch.node2 =  IP2:PORT
# prod.nsg1.apps.db.elasticsearch.node3 =  IP1:PORT (Consider that we always have to generate 3 node keys, if 2 are available, repeat the first, if 1 available repeat the previous rule )
  # endpoints: []
  # Configuration required is:
  # prod.nsg1.apps.db.elasticsearch.node1 = "************:9202" pod-1.headlessservice.svc.cluster.local:9202 or servicename.svc.cluster.local
  # prod.nsg1.apps.db.elasticsearch.node2 = "************:9202"
  # prod.nsg1.apps.db.elasticsearch.node3 = "***********:9202"

# ALSO Include the non multi env configs as well for the above.

  # Note, it always require three endpoints. Repeated endpoints are added on stage.


redis:
  cluster: ""
  port: ""
  




############################################################################################
#Random values to make this work:
############################################################################################

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# ingress:
#   enabled: false
#   className: ""
#   annotations: {}
#     # kubernetes.io/ingress.class: nginx
#     # kubernetes.io/tls-acme: "true"
#   hosts:
#     - host: chart-example.local
#       paths:
#         - path: /
#           pathType: ImplementationSpecific
#   tls: []
#   #  - secretName: chart-example-tls
#   #    hosts:
#   #      - chart-example.local

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# image:
#   repository: nginx
#   pullPolicy: IfNotPresent
#   # Overrides the image tag whose default is the chart appVersion.
#   tag: ""

