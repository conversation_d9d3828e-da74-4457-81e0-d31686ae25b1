# Overriden by namespace provided in helm install
# namespace: kalpha-stage-backend

# For devops: set to true for debugging the init job 
scriptDebuggingEnabled: false

#REQUIRED
# Sets the service name
serviceName: "n-live-data-streaming" 

#REQUIRED
# Sets the environment
environment: "kalpha" 

#REQUIRED
# prod/stage # Used for creating the service conf
envType: "stage" 

# REQUIRED
# can be prod.conf or prod_conf.conf for few services, check chef before deploy
configurationFile: "/app/conf/application.conf"

# This is used for configuration value multienv.hostname_suffix and multienv.admin_hostname_suffix (Not sure if this is actively used)
hostnameSuffix: ".strawmine.com" # Default is .strawmine.com

# OPTIONAL NOT REQUIRED TO BE FILLED.
# Defaults to <env>-service-endpoints
# This configmap contains the service endpoints which are available in override conf for accessing other backend services
# Created by the configmap script: update-service-endpoints-cm
# update-service-endpoints-cm runs for each service during deployment and updates the configmap.
# serviceEndpointsConfigMap: ""


# List of service endpoints which can be filled in as override in override.conf .
# As the values are different here for different environment, complete value is expected.
# Example:
overrideServiceEndpoints: 
  stage.kalpha.n-device-registration-service-url: "http://nsg1-n-device-registration.ncinga.com"
  stage.kalpha.n-event-hub-service-url: "http://nsg1-n-event-hub.ncinga.com"
  stage.kalpha.categories-service-url: "http://kalpha-categories.stage-backend.svc.cluster.local"

# overrideServiceEndpoints: {}


#List of key value which will be overridden 

# These values will be mounted as such in override-conf
# Example:
overrideServiceConfs:
    stage.kalpha.s3-bucket.name: "test-bucket"
    stage.kalpha.s3-bucket.region: "ap-southeast-1a"
# overrideServiceConfs: {}


###################################################
# TODO: Add overrideServiceConfConfigMap implementation.
#
#
###################################################
# Can also provide the name of an existing ConfigMap and the key to use within that ConfigMap. This would be added along with the keys provided in overrideServiceConfs.
# Example: Create a configmap like this to store the configs
# 
# apiVersion: v1
# kind: ConfigMap
# metadata:
#   name: custom-override-configmap
# data:
#   override-values: |
#     prod.nsg1.s3-bucket.name: "test-bucket"
#     prod.nsg1.s3-bucket.region: "ap-southeast-1"
#     prod.nsg1.api-endpoint: "http://api.nsg1-prod.com"
#     prod.nsg1.db-host: "db.nsg1-prod.com"

# Usage:
# overrideServiceConfConfigMap:
#   name: "custom-override-configmap"
#   key: "override-values"
# overrideServiceConfConfigMap:
#     name: ""
#     key: ""




# For sensitive information, provide the name of an existing Secret and the key to use within that Secret.
# This Secret can contain sensitive data like passwords, API keys, etc.
# Creation guide:
# Create a file (sensitive-values.txt) containing the data:
# cat <<EOF > sensitive-values.txt
# prod.nsg1.db-password: "securePassword"
# prod.nsg1.api-key: "apikey12345"
# EOF

# base64 sensitive-values.txt > sensitive-values-encoded.txt

# kubectl create secret generic custom-override-secret \
#   --from-literal=sensitive-values="$(cat sensitive-values-encoded.txt)" \
#   -n <namespace>

# Usage:
# overrideServiceConfSecret:
#   name: "custom-override-secret"
#   key: "sensitive-values"

overrideServiceConfSecret:
    name: 
    key: 


# Database configuration:
# If enabled is true while creating the helm chart, it will create the password secrets. These secrets are run in pre-hook, so later
# After installing helm chart, if enabled is turned to true from false, the secret needs to be created manually, or install the helm chart again.
# postgres:
#   enabled: true
#   namespace: stage-backend-postgres # Create if not exist
#   # In case of external, we create a headless clusterIP service and set the endpoint to IP address of the external service.
#   external:
#     external-postgres: true
#     cluster: stage-nalpha-1.stage-backend-postgres.svc.cluster.local
#     IP: ***********
#     port: 5432 # Defaults to 5432 
#   username: ""  # Defaults to <qualifiedServiceName> replacing hyphen with underscore
#   password: "" # To be generated or picked from a secret
#   dbName: "" # Defaults to the postgres.username
#   # cluster: "postgres-cluster-pg-d-pgbouncer.postgres.svc.cluster.local" # Postgres service endpoint
#   cluster: "stage-nalpha-1.stage-backend-postgres.svc.cluster.local" # Postgres service endpoint
#   port: "" # Defaults to 5432
#   dbConnectionpools:
#     - name: "default"
#       maxConnections: "100"
#       minConnections: "50"
#       numThreads: "100"
#       connectionTimeout: "4000"
#       registerMbeans: true

#     - name: "userfacing-connections"
#       maxConnections: "20"
#       minConnections: "200"
#       numThreads: "2000"
#       connectionTimeout: "24000"
#       registerMbeans: true

# How does postgres setup work?
# This setup runs a job which creates the user, and its access and db if required
# Creation:
# - secret with username password details
# - namespace, headless service and external endpoint in case it connects to external postgres service
# - If internal on k8s, then it expects that serviceName and namespace are given correctly.
# - Identifies the database with service serviceName.namespace.svc.cluster.local in this case: stage-nalpha-1.stage-backend-postgres.svc.cluster.local


# Dependency:
# - database admin credentials.
# {
#     "mongo": {
#         "stage-nalpha-mongo3.stage-backend-mongo.svc.cluster.local": {
#             "admin_user": "zladmin",
#             "admin_password": "aMc9Da7N10jPk6hj"
#         },
#         "stage-nalpha-mongo4.stage-backend-mongo.svc.cluster.local": {
#             "admin_user": "zladmin",
#             "admin_password": "aMc9Da7N10jPk6hj"
#         }
#     },
#     "postgres": {
#         "stage-nalpha-1.stage-backend-postgres.svc.cluster.local": {
#             "port": "5432",
#             "admin_user": "stage_nalpha_1",
#             "admin_password": "FVONdGXKhHZr7vN1"
#         }
#     }
# }

# In our configuration, we will connect to the databases using serviceName and postgres.port.
# While the enpoint will be configured using the postgres.external.port
postgres:
  enabled: true        # Required if `enabled` is true
  username: ""  # Defaults to <qualifiedServiceName> replacing hyphen with underscore
  # password: ""  # Generated by helm and stored in secret
  dbName: ""    # Defaults to the postgres.username
  port: "5432" # Port at which the service listens to. Should be same as the port where the postgres is running
  cluster: "stage-nint-new.cyiz7opktgoc.ap-southeast-1.rds.amazonaws.com"
  dbConnectionpools:
    - name: "default"
      maxConnections: "10"
      minConnections: "5"
      numThreads: "10"
      connectionTimeout: "4000"
      registerMbeans: true

#  For mongo3 
# external:
#     enabled: true  
#     ip: "*************" 
# port: 27012

# # For mongo 4
# external:
#     enabled: true  
#     ip: "************"     
# port: 27112
# 

# There are two ports here.
# mongo.external.port is being used for defining the external endpoint.
# mongo.port is the port at which the kubernetes service listens to
mongo:
  enabled: true
  mongoUri: "mongodb://************:27017,***********:27017,***********:27017/?replicaSet=eks-mongo-3"
  external:
    enabled: true  # Set to true for using an external PostgreSQL
    ip: "************"           # Required if `enabled` is true
    port: "27017"
  username: ""  # Defaults to <environment>-<serviceName> replacing hyphen with underscore
  # password: "" # To be generated
  authenticationDatabase: "admin" # Defaults to NSP_Users. This is the database where your service user will be created.
  dbToConnect: "admin" # Defaults to NSP_Users. This is the database where the mongo will connect.
  cluster: "************:27017,***********:27017,***********:27017" # Postgres service endpoint
  # TODO: store replicasetName name in  db-admin
  replicasetName: "eks-mongo-3" 
  port: "27017" # This is the service port.
  dbRoles:
    - role: "readWrite"
      db: "orders"

    - role: "read"
      db: "sales"


# For kafka, as we have not tested if the library provide support for resolving DNS names, we are sticking to IP
# Long term sugested setup:
# - Use strimzi kafka and check if the service is supporting to use headless service name for bootstrap.servers
# - check if in the service, the endpoint provided in override conf for kafka can be a DNS name.
# For now, we need to pass all the kafka nodes in the cluster, and also mention the node which is 
# master (Although concept of kafka master server doesn't exist, still in our backend configuration, we are connecting with just one kafka endpoint instead of providing all the brokers)
# 
# For now we are assuming connection to kafka rest on one server only. i.e. master.
# We will enable the distribution later.
kafka:
  enabled: true
  restEnabled: false
  serviceName: "stage-nalpha-kafka"
  namespace: "stage-kafka"
  # The service will have endpoint of masterEndpoint:port and masterEndpoint:restPort.
  port: 9093
  restPort: 8080
  zookeeperPort: 2181
  masterEndpoint: "************"
  producer:
    acks: -1    #Adding Acknowledgement from all insync replicas , to ensure reliability
    producerType: "sync"     # specifies whether the messages are sent asynchronously (async) or synchronously (sync)
    messageMaxBytes: "10485760"
    maxRequestSize: "10485760"
  consumer:
    maxPollIntervalMs: "864000000" # Defaults to 10 days
    messageMaxBytes: "10485760"
  # List of broker IP addresses. This will be used in kafka producer configuration.
  # broker_endpoints: []
  brokerEndpoints:
    - "************"
    - "***********"
    - "***********"



analyticKafka:
  enabled: false
  restEnabled: false
  serviceName: "stage-nalpha-analytic-kafka-rest"
  namespace: "stage-analytic-kafka"  # Namespace for analytic-kafka service
  # The service will have endpoint of masterEndpoint:port and masterEndpoint:restPort.
  port: 9092
  restPort: 8082
  zookeeperPort: 2181
  masterEndpoint: "************"
  # List of broker IP addresses. This will be used in kafka producer configuration.
  # broker_endpoints: []
  brokerEndpoints:
    - "************"
  # brokerEndpoints:
  #   - "*************"
  #   - "*************"


# If the service requires admin endpoint. nsg1-n-data-integrations.admin.ncinga.com -> nsg1-n-data-integrations.ncinga.com
# So, will create ingress with <env>-<service>.admin.strawmine.com which will point to the service nsg1-n-data-integrations.<ns>.svc.cluster.local
# This ingress should be public but it should be accessible only with google auth.

# We can have multiple ingress for the same host and they will be merged to the single server block.
# Each domain has a single server block.
# So any server-snippet added will be added to the server block of that host.
# Keep server snippet in one ingress only, as the first ingress applied will be kept and the rest of the server snippet will be discarded.
# The location blocks should not be overriten in different ingress as this will cause the merge to fail.
# configuration-snippet is added to all the location blocks of a given ingress.
# - For example, if we require to have a websocket request for a service along with non ws, we can have two ingresses.
# For every service there are two ingress:
# admin ingress: Individual ingress
# external host ingress: Ingress with multiple services

# ======================================================================================================================================================================
# If httpContext is not empty: create two location blocks in each ingress and separate ingresses for each location blocks

# ext_enabled_or_disabled                                                      http_context_enabled                        http_context_disabled                          
# ext_enabled :                # location: /auth/ext/wsapi/(.*)                /httpContext/ext/wsapi/$1                   /ext/wsapi/$1           
# ext_enabled :                # location: /auth/httpContext/ext/wsapi/(.*)    /httpContext/ext/wsapi/$1                   /ext/wsaapi/$1  

# ext_enabled :                # location: /auth/ext/(.*)                      /httpContext/ext/$1                         /ext/$1           
# ext_enabled :                # location: /auth/httpContext/ext/(.*)          /httpContext/ext/$1                         /ext/$1  


# ext_disabled:                # location: /auth/wsapi/(.*)                    /httpContext/wsapi/$1                       /wsapi/$1           
# ext_disabled:                # location: /auth/httpContext/wsapi/(.*)        /httpContext/wsapi/$1                       /wsaapi/$1  

# ext_disabled:                # location: /auth/(.*)                          /httpContext/$1                             /$1           
# ext_disabled:                # location: /auth/httpContext/(.*)              /httpContext/$1                             /$1  

# # If httpContext is empty: create only one location block
# ext_enabled :                # location: /auth/ext/wsapi/(.*)                                                           /ext/wsapi/$1           

# ext_disabled:                # location: /auth/wsapi/(.*)                                                               /wsapi/$1           

# ext_enabled :                # location: /auth/ext/(.*)                                                                 /ext/$1           

# ext_disabled:                # location: /auth/(.*)                                                                     /$1           

# ======================================================================================================================================================================

# General nginx ingress annotations:
# nginx.ingress.kubernetes.io/proxy-body-size: 8m : client_max_body_size
# nginx.ingress.kubernetes.io/proxy-connect-timeout: 300
# nginx.ingress.kubernetes.io/proxy-send-timeout:	300
# nginx.ingress.kubernetes.io/proxy-read-timeout:	300


ingress:
  className: "external-nginx"
  http_context:
    context: "blz/flow_service_dbsvc"
    enabled: false
  blocks: 
    - name: "root"
      location: "/"
      additionalAnnotations: {}
    - name: "admin"
      location: "/admin"
      additionalAnnotations: {}

    # - name: "websocket"
    #   location: "/wsapi"
    #   # Any location specific annotations can be added here: eg: body size, or
    #   additionalAnnotations: {}
  client_endpoint:
    - name: "n-live-data-streaming-web" # This is irrelevant but is used for readability
      host: "kalpha-service-external.strawmine.com"
      serviceIdentifier: "flow_services"
      ext_enabled: false
      annotations: {}
    - name: "n-live-data-streaming-web-wsapi" # This is irrelevant but is used for readability
      host: "kalpha-service-external.strawmine.com"
      serviceIdentifier: "flow_service/wsapi"
      ext_enabled: false
      annotations: {}
  admin_endpoint:
    - name: "live-data-streaming admin endpoint"
      host: "kalpha-n-live-data-streaming.admin.strawmine.com"
      annotations: 
      # annotations: {}
        nginx.ingress.kubernetes.io/auth-signin: https://oauth2-solvei8.strawmine.com/oauth2/start?rd=/redirect/$http_host$escaped_request_uri
        nginx.ingress.kubernetes.io/auth-url: https://oauth2-solvei8.strawmine.com/oauth2/auth


s3:
  enabled: true
  s3ObjectStorageInterfaceFactoryModuleEnabled: false
  region: "" # Defaults to ap-southeast-1
  access: #List of all the buckets for which access is required.
    - bucketName: solvei8-stage-kalpha-n-live-data-streaming
      acl: write # it can be read or write


initJob:
  enabled: true
  serviceAccount: backend-service-init-job-sa # This is created by microservice-commons-chart



replicaCount: 3
image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/zilingo-live-data-streaming-backend
#  tag: "4be4ee227561e39bf2a42a6e3d52020b097599a9"
  tag: "latest"
  pullPolicy: IfNotPresent
#  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.

service:
  type: ClusterIP
  ports:
    - name: http
      port: 80
      targetPort: 9000

    - name: jmx-port
      port: 9050
      targetPort: 9050
    
    - name: otlp-port
      port: 4317
      targetPort: 4317

  port: 80
  targetPort: 9000



# Liveness probe configuration
livenessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

# Readiness probe configuration
readinessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 10
  periodSeconds: 15
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

# Resources for the backend pod
resources: {}
# resources:
#   limits:
#     cpu: "500m"
#     memory: "512Mi"
#   requests:
#     cpu: "250m"
#     memory: "256Mi"

hostAliases:
  - ip: "************"
    hostnames:
      - "alpha-unified-id.strawmine.com"


java:
  agents:
    - name: "jmx_prometheus_javaagent"
      path: "/opt/javaagents/jmx_prometheus_javaagent-0.20.0.jar=9050:/opt/service-confs/jmx_prometheus_config.yaml"
      options: ""
    - name: "opentelemetry_javaagent"
      path: "/opt/javaagents/opentelemetry-javaagent.jar"
      options: "-Dotel.resource.attributes=qualified.service.name=${QUALIFIED_SERVICE_NAME},host.name=$POD_NAME -Dotel.javaagent.debug=false -Dotel.exporter.otlp.protocol=grpc -Dotel.exporter.otlp.endpoint=http://alloy.observability-alloy.svc.cluster.local:4317 -Dotel.traces.exporter=otlp -Dotel.logs.exporter=otlp -Dotel.metric.export.interval=15000 -Dotel.metrics.exporter=otlp -Dotel.service.name=${QUALIFIED_SERVICE_NAME}"
  
    # - name: "opentelemetry_javaagent"
    #   path: "/opt/javaagents/opentelemetry-javaagent.jar"
    #   options: "-Dotel.resource.attributes=qualified.service.name=${QUALIFIED_SERVICE_NAME},host.name=$POD_NAME -Dotel.javaagent.debug=true -Dotel.traces.exporter=none -Dotel.logs.exporter=logging -Dotel.metrics.exporter=none -Dotel.service.name=${QUALIFIED_SERVICE_NAME}"
  
    # - name: "opentelemetry_javaagent"
    #   path: "/opt/javaagents/opentelemetry-javaagent.jar"
    #   options: "-Dotel.resource.attributes=qualified.service.name=${QUALIFIED_SERVICE_NAME},host.name=$POD_NAME -Dotel.javaagent.debug=false -Dotel.exporter.otlp.protocol=grpc -Dotel.exporter.otlp.endpoint=http://localhost:4317 -Dotel.traces.exporter=otlp -Dotel.logs.exporter=otlp -Dotel.metric.export.interval=15000 -Dotel.metrics.exporter=otlp -Dotel.service.name=${QUALIFIED_SERVICE_NAME}"
  
  additionalOpts: ""

  # additionalOpts: "-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=*:5005"

# Application-specific arguments (APP_ARGS)
app:
  # logDir: "/var/log/n-live-data-streaming"
  pidfilePath: "/dev/null"
  configFile: "/opt/service-confs/override.conf" # This is the location to put all the override configs. This will include the absolute paths of other configs.
  # loggerFile: "${logger_file}"
  # serviceInstanceRole: "${SERVICE_INSTANCE_ROLE}"
  serviceInstanceNodeId: "9999" # Not sure if this is used by backend service
  statusReportStartedBy: "ops-user" # TODO: Set this up with argoCD pipeline.
  javaTmpDir: "/tmp/n-live-data-streaming"
# TODO: Add heapdumps to s3 in service init.
# Whenever the service starts, it should check if there is any file in /opt/heapdumps/
# /opt/heapdumps/ is a volume attached to each pod.
# init job should send this to s3, and clear the volume.
# Volume can be of size 2G
# 
# JVM-specific arguments (JVM_ARGS)
jvm:
  heapDumpPath: "/opt/heapdumps"
# JVM memory settings (JVM_MEMORY_ARGS)
  memory:
    Xms: "350m"
    Xmx: "350m"

# # Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# # - name: foo
# #   mountPath: "/etc/foo"
# #   readOnly: true

# Heapdump EFS volume configuration
heapdump:
  enabled: true
  storage: "2Gi"
  storageClassName: "efs-sc"
  efsFileSystemId: "fs-0f5d0ec509c876861"  # Replace with actual EFS file system ID
  efsPath: "/heapdumps"  # Optional: subdirectory within EFS

multiDCServiceUnified: false


# Some additional configurations required by backend services. These configs don't change so they will be part of default.
# This will make clusterIP service type with load balancing for connecting to external cluster
druid:
  namespace: "stage-druid-cluster"  # Namespace for druid service
  coordinator:
    enabled: false
    port: 8081
    serviceName: "stage-nalpha-druid-coordinator"
    endpoints: [ "************" ]
  broker:
    enabled: false
    port: 8082
    serviceName: "stage-nalpha-druid-broker"
    endpoints: [ "***********" ]

# TODO add these to override-conf

elasticsearch:
  enabled: true # if it is required by the service or not
  namespace: "stage-elasticsearch-cluster"  # Namespace for Elasticsearch service
  serviceName:
  port: 9202
  external: 
    enabled: true # true or false
    # endpoints: [] # If we are using ES on VM, we want the IP address and port for them
    IPs:
      - IP1
      - IP2
      - IP3

# For k8s based ES: 
# prod.nsg1.apps.db.elasticsearch.node1 =  clusterIPservicename.ns.svc.cluster.local:PORT
# prod.nsg1.apps.db.elasticsearch.node2 =  clusterIPservicename.ns.svc.cluster.local:PORT
# prod.nsg1.apps.db.elasticsearch.node3 =  clusterIPservicename.ns.svc.cluster.local:PORT

# For external based ES:
# prod.nsg1.apps.db.elasticsearch.node1 =  IP1:PORT
# prod.nsg1.apps.db.elasticsearch.node2 =  IP2:PORT
# prod.nsg1.apps.db.elasticsearch.node3 =  IP1:PORT (Consider that we always have to generate 3 node keys, if 2 are available, repeat the first, if 1 available repeat the previous rule )
  # endpoints: []
  # Configuration required is:
  # prod.nsg1.apps.db.elasticsearch.node1 = "************:9202" pod-1.headlessservice.svc.cluster.local:9202 or servicename.svc.cluster.local
  # prod.nsg1.apps.db.elasticsearch.node2 = "************:9202"
  # prod.nsg1.apps.db.elasticsearch.node3 = "***********:9202"

# ALSO Include the non multi env configs as well for the above.

  # Note, it always require three endpoints. Repeated endpoints are added on stage.


redis:
  cluster: ""
  port: ""
  




############################################################################################
#Random values to make this work:
############################################################################################

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# ingress:
#   enabled: false
#   className: ""
#   annotations: {}
#     # kubernetes.io/ingress.class: nginx
#     # kubernetes.io/tls-acme: "true"
#   hosts:
#     - host: chart-example.local
#       paths:
#         - path: /
#           pathType: ImplementationSpecific
#   tls: []
#   #  - secretName: chart-example-tls
#   #    hosts:
#   #      - chart-example.local

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# image:
#   repository: nginx
#   pullPolicy: IfNotPresent
#   # Overrides the image tag whose default is the chart appVersion.
#   tag: ""

