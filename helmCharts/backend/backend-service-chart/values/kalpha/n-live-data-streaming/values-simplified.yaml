# Service-specific configuration for n-live-data-streaming
serviceName: "n-live-data-streaming"

# Service-specific endpoint overrides
overrideServiceEndpoints: 
  stage.kalpha.n-device-registration-service-url: "http://nsg1-n-device-registration.ncinga.com"
  stage.kalpha.n-event-hub-service-url: "http://nsg1-n-event-hub.ncinga.com"
  stage.kalpha.categories-service-url: "http://kalpha-categories.stage-backend.svc.cluster.local"

# Service-specific configuration overrides
overrideServiceConfs:
  stage.kalpha.s3-bucket.name: "test-bucket"
  stage.kalpha.s3-bucket.region: "ap-southeast-1a"

# Database configurations - service specific overrides
postgres:
  enabled: true
  cluster: "stage-nint-new.cyiz7opktgoc.ap-southeast-1.rds.amazonaws.com"

mongo:
  enabled: true
  mongoUri: "mongodb://************:27017,***********:27017,***********:27017/?replicaSet=eks-mongo-3"
  external:
    enabled: true
    ip: "************"
  dbToConnect: "admin"
  cluster: "************:27017,***********:27017,***********:27017"
  replicasetName: "eks-mongo-3"
  dbRoles:
    - role: "readWrite"
      db: "orders"
    - role: "read"
      db: "sales"

# Kafka configuration - service specific
kafka:
  enabled: true

# Analytic Kafka configuration - service specific
analyticKafka:
  enabled: true
  serviceName: "stage-nalpha-analytic-kafka-rest"
  namespace: "stage-analytic-kafka"
  restPort: 8090
  zookeeperPort: 2182
  brokerEndpoints:
    - "************"
    - "***********"
    - "***********"

# Service-specific ingress configuration
ingress:
  className: "external-nginx"
  http_context:
    context: "blz/streaming_services"
    enabled: false
  blocks: 
    - name: "root"
      location: "/"
      additionalAnnotations: 
        nginx.ingress.kubernetes.io/proxy-connect-timeout: "3"
        nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/client-max-body-size: "50m"
    - name: "admin"
      location: "/admin"
      additionalAnnotations: 
        nginx.ingress.kubernetes.io/proxy-connect-timeout: "3"
        nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/client-max-body-size: "50m"
  client_endpoint: 
    - name: "n-live-data-streaming" 
      host: "kalpha-service-external.strawmine.com"
      serviceIdentifier: "streaming_services"
      ext_enabled: true
      annotations: {}
  admin_endpoint: 
    - name: "n-live-data-streaming endpoint"
      host: "kalpha-n-live-data-streaming.admin.strawmine.com"
      annotations: {}

# S3 configuration - service specific
s3:
  enabled: true
  access:
    - bucketName: solvei8-stage-kalpha-n-live-data-streaming
      acl: write

# Init job configuration - service specific
initJob:
  enabled: true

# Service-specific deployment configuration
replicaCount: 1
image:
  repository: 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/zilingo-live-data-streaming-backend
  tag: "latest"

# Service-specific probe configuration
livenessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true

readinessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  initialDelaySeconds: 10
  periodSeconds: 15

# Service-specific Java agent configuration
java:
  agents:
    - name: "jmx_prometheus_javaagent"
      path: "/opt/javaagents/jmx_prometheus_javaagent-0.20.0.jar=9050:/opt/service-confs/jmx_prometheus_config.yaml"
      options: ""
    - name: "opentelemetry_javaagent"
      path: "/opt/javaagents/opentelemetry-javaagent.jar"
      options: "-Dotel.resource.attributes=qualified.service.name=${QUALIFIED_SERVICE_NAME},host.name=$POD_NAME -Dotel.javaagent.debug=false -Dotel.exporter.otlp.protocol=grpc -Dotel.exporter.otlp.endpoint=http://alloy.observability-alloy.svc.cluster.local:4317 -Dotel.traces.exporter=otlp -Dotel.logs.exporter=otlp -Dotel.metric.export.interval=15000 -Dotel.metrics.exporter=otlp -Dotel.service.name=${QUALIFIED_SERVICE_NAME}"

# Service-specific application configuration
app:
  javaTmpDir: "/tmp/n-live-data-streaming"

# Service-specific JVM memory settings
jvm:
  memory:
    Xms: "350m"
    Xmx: "350m"

# Heapdump EFS volume configuration
heapdump:
  enabled: true
  efsFileSystemId: "fs-0f5d0ec509c876861"

# Service-specific Druid configuration
druid:
  coordinator:
    endpoints: [ "************" ]
  broker:
    endpoints: [ "***********" ]

# Service-specific Elasticsearch configuration
elasticsearch:
  enabled: true
  external: 
    enabled: true
    endpoints:
      - "************"
      - "***********"
      - "***********"
    IPs:
      - "************"
      - "***********"
      - "***********"
