{{- if.Values.initJob.enabled }}
{{- $scriptDebuggingEnabled := (default "false" .Values.scriptDebuggingEnabled) | toString }}

{{- $envType := .Values.envType }}
{{- $env := .Values.environment }}
{{- $serviceName := .Values.serviceName }}

{{- /* Mongo values */}}

{{- $isMongoEnabled := .Values.mongo.enabled }}

{{- $mongoHost := .Values.mongo.mongoUri }}
{{- $mongoServiceUser := default (printf "%s-%s" $env $serviceName | replace "-" "_") .Values.mongo.username }}
{{- $mongoAuthenticationDatabase := default "NSP_Users" .Values.mongo.authenticationDatabase }}

{{- $mongoRoles := list }}
{{- range .Values.mongo.dbRoles }}
  {{- $role := dict "role" .role "db" .db }}
  {{- $mongoRoles = append $mongoRoles $role }}
{{- end }}
{{- $mongoRolesJson := toJson $mongoRoles | replace "\"" "\\\"" }}
{{- $mongoSecretName := printf "%s-%s-mongo-pass" $env $serviceName }}


{{- /* PSQL values */}}
{{- $isPsqlEnabled := .Values.postgres.enabled }}
{{- $psqlHost := .Values.postgres.cluster }}
{{- $psqlPortToConnect := (default "5432" .Values.postgres.port) | toString }}
{{- $psqlServiceUser := default (printf "%s-%s" $env $serviceName | replace "-" "_") .Values.postgres.username }}
{{- $psqlServiceDb := default (printf "%s_%s" $env $serviceName | replace "-" "_") .Values.postgres.dbName }}
{{- $psqlSecretName := printf "%s-%s-postgres-pass" $env $serviceName }}

{{- /* S3 values */}}
{{- $isS3Enabled := .Values.s3.enabled -}}
{{- $s3Region := default "ap-southeast-1" .Values.s3.region -}}
{{- $s3Roles := list }}
{{- range .Values.s3.access }}
  {{- $role := dict "bucketName" .bucketName "acl" .acl }}
  {{- $s3Roles = append $s3Roles $role }}
{{- end }}
{{- $s3RolesJson := toJson $s3Roles | replace "\"" "\\\"" }}

{{- $namespace := .Release.Namespace -}}
{{- $initJobServiceAccount := default "backend-service-init-job-sa" .Values.initJob.serviceAccount  }}

apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $env }}-{{ $serviceName }}-init
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,pre-upgrade
    {{- if eq $scriptDebuggingEnabled "false" }}
    "helm.sh/hook-delete-policy": hook-succeeded
    {{- end }}
spec:
  template:
    spec:
      serviceAccountName: {{ $initJobServiceAccount }}
      restartPolicy: Never
      volumes:
        - name: scripts-configmap # Reference to the combined ConfigMap
          configMap:
            name: backend-scripts-configmap
        {{- if or $isMongoEnabled $isPsqlEnabled }}
        - name: db-admin-secret
          secret:
            secretName: db-admin-secret
        {{- end }}
        # {{- if $isMongoEnabled }}
        # - name: mongo-service-secret
        #   secret:
        #     secretName: {{ $mongoSecretName }}
        # {{- end }}
        # {{- if $isPsqlEnabled }}
        # - name: postgres-service-secret
        #   secret:
        #     secretName: {{ $psqlSecretName }}
        # {{- end }}
      containers:
        - name: backend-service-init
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/backend-init-job-debian:v5
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"
          env:
            - name: ENVIRONMENT
              value: {{ $env }}
            - name: SERVICE_NAME
              value: {{ $serviceName }}
            - name: ENV_TYPE
              value: {{ $envType }}
            - name: IS_MONGO_ENABLED
              value: "{{ $isMongoEnabled }}"
            - name: IS_PSQL_ENABLED
              value: "{{ $isPsqlEnabled }}"
            - name: IS_S3_ENABLED
              value: "{{ $isS3Enabled }}"
            - name: SCRIPT_DEBUGGING_ENABLED
              value: "{{ $scriptDebuggingEnabled }}"
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            {{- if $isMongoEnabled }}
            - name: MONGO_ROLES_JSON
              value: "{{ $mongoRolesJson }}"
            - name: MONGO_HOST
              value: {{ $mongoHost }}
            - name: MONGO_ADMIN_USERNAME
              value: "admin"  # Replace with actual admin username or use a secret
            - name: MONGO_ADMIN_PASSWORD
              value: "StrongPassword123"
            - name: MONGO_SERVICE_USER
              value: {{ $mongoServiceUser }}
            - name: MONGO_AUTHENTICATION_DATABASE
              value: {{ $mongoAuthenticationDatabase }}
            {{- end }}
            {{- if $isPsqlEnabled }}
            - name: PSQL_HOST
              value: {{ $psqlHost }}
            - name: PSQL_PORT
              value: "{{ $psqlPortToConnect }}"
            - name: PSQL_SERVICE_USER
              value: {{ $psqlServiceUser }}
            - name: PSQL_SERVICE_DB
              value: {{ $psqlServiceDb }}
            {{- end }}
            {{- if $isS3Enabled }}
            - name: S3_ROLES_JSON
              value: "{{ $s3RolesJson }}"
            - name: S3_REGION
              value: {{ $s3Region }}
            {{- end }}
          volumeMounts:
            - name: scripts-configmap
              mountPath: /scripts
            {{- if or $isMongoEnabled $isPsqlEnabled }}
            - name: db-admin-secret
              mountPath: /etc/db-admin-secret
              readOnly: true
            {{- end }}
            {{- if $isMongoEnabled }}
            - name: mongo-service-secret
              mountPath: /etc/mongo-service-secret
              readOnly: true
            {{- end }}
            {{- if $isPsqlEnabled }}
            - name: postgres-service-secret
              mountPath: /etc/postgres-service-secret
              readOnly: true
            {{- end }}
          command: ["/bin/bash", "-c", "--"]
          args:
            - |
              # tail -f /dev/null
              # bash /scripts/configure-service.sh
              bash /scripts/configure-service.sh
{{- end }}