{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-cleanup
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
spec:
  schedule: "*/15 * * * *"  # Every 15 minutes
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "backend-service-chart.selectorLabels" . | nindent 12 }}
            component: heapdump-cleanup
        spec:
          restartPolicy: OnFailure
          serviceAccountName: {{ include "backend-service-chart.serviceAccountName" . }}-heapdump-cleanup
          containers:
          - name: heapdump-cleanup
            image: amazon/aws-cli:2.13.25
            imagePullPolicy: IfNotPresent
            env:
            - name: AWS_DEFAULT_REGION
              value: {{ .Values.heapdump.s3Region | default "ap-southeast-1" }}
            - name: S3_BUCKET
              value: {{ .Values.heapdump.s3Bucket | default "stage-k8s-heapdumps" }}
            - name: SERVICE_NAME
              value: {{ $qualifiedServiceName }}
            command:
            - /bin/bash
            - -c
            - |
              set -e
              echo "Starting heapdump cleanup job for service: $SERVICE_NAME"
              echo "S3 Bucket: $S3_BUCKET"
              echo "AWS Region: $AWS_DEFAULT_REGION"
              
              # Function to upload file to S3 and remove local copy
              upload_and_cleanup() {
                local file_path="$1"
                local pod_name="$2"
                local file_name=$(basename "$file_path")
                local s3_key="${pod_name}/${file_name}"
                
                echo "Found heapdump file: $file_path"
                echo "Uploading to S3: s3://$S3_BUCKET/$s3_key"
                
                # Upload to S3
                if aws s3 cp "$file_path" "s3://$S3_BUCKET/$s3_key" --no-progress; then
                  echo "Successfully uploaded $file_name to S3"
                  
                  # Verify upload by checking if file exists in S3
                  if aws s3 ls "s3://$S3_BUCKET/$s3_key" > /dev/null 2>&1; then
                    echo "Verified file exists in S3, removing local copy"
                    rm -f "$file_path"
                    echo "Local file $file_path removed"
                  else
                    echo "ERROR: File not found in S3 after upload, keeping local copy"
                  fi
                else
                  echo "ERROR: Failed to upload $file_name to S3"
                fi
              }
              
              # Check if heapdumps directory exists
              if [ ! -d "/opt/heapdumps" ]; then
                echo "Heapdumps directory not found, nothing to process"
                exit 0
              fi
              
              # Process each pod directory
              processed_files=0
              for pod_dir in /opt/heapdumps/*/; do
                if [ -d "$pod_dir" ]; then
                  pod_name=$(basename "$pod_dir")
                  echo "Checking pod directory: $pod_name"
                  
                  # Find all .hprof files in the pod directory
                  find "$pod_dir" -name "*.hprof" -type f | while read -r heapdump_file; do
                    if [ -f "$heapdump_file" ]; then
                      upload_and_cleanup "$heapdump_file" "$pod_name"
                      processed_files=$((processed_files + 1))
                    fi
                  done
                  
                  # Also check for any java_pid*.hprof files (alternative naming)
                  find "$pod_dir" -name "java_pid*.hprof" -type f | while read -r heapdump_file; do
                    if [ -f "$heapdump_file" ]; then
                      upload_and_cleanup "$heapdump_file" "$pod_name"
                      processed_files=$((processed_files + 1))
                    fi
                  done
                fi
              done
              
              echo "Heapdump cleanup job completed"
              if [ $processed_files -eq 0 ]; then
                echo "No heapdump files found to process"
              else
                echo "Processed $processed_files heapdump files"
              fi
            volumeMounts:
            - name: heapdump-volume
              mountPath: /opt/heapdumps
            resources:
              requests:
                memory: "64Mi"
                cpu: "50m"
              limits:
                memory: "128Mi"
                cpu: "100m"
          volumes:
          - name: heapdump-volume
            persistentVolumeClaim:
              claimName: {{ printf "%s-%s" .Values.environment .Values.serviceName }}-heapdump-pvc
{{- end }}
