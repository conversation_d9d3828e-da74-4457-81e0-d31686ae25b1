{{- $envType := .Values.envType }}
{{- $env := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- /* Mongo values */}}

{{- $isMongoEnabled := .Values.mongo.enabled }}
{{- $mongoHost := default (printf "%s.%s.svc.cluster.local" .Values.mongo.serviceName .Values.mongo.namespace) .Values.mongo.mongoUri }}
{{- $mongoServiceUser := default (printf "%s-%s" $env $serviceName | replace "-" "_") .Values.mongo.username }}
{{- $mongoAuthenticationDatabase := default "NSP_Users" .Values.mongo.authenticationDatabase }}
{{- $mongoRoles := list }}
{{- range .Values.mongo.dbRoles }}
  {{- $role := dict "role" .role "db" .db }}
  {{- $mongoRoles = append $mongoRoles $role }}
{{- end }}
{{- $mongoRolesJson := toJson $mongoRoles | replace "\"" "\\\"" }}

{{- /* PSQL values */}}
{{- $isPsqlEnabled := .Values.postgres.enabled }}
{{- $psqlHost := .Values.postgres.cluster }}
{{- $psqlPort := default "5432" .Values.postgres.port }}
{{- $psqlServiceUser := default (printf "%s-%s" $env $serviceName | replace "-" "_") .Values.postgres.username }}
{{- $psqlServiceDb := default (printf "%s_%s" $env $serviceName | replace "-" "_") .Values.postgres.dbName }}

{{- /* S3 values */}}
{{- $isS3Enabled := .Values.s3.enabled -}}
{{- $isS3ObjectStorageInterfaceFactoryModuleEnabled := .Values.s3.s3ObjectStorageInterfaceFactoryModuleEnabled -}}

{{- $s3Region := default "ap-southeast-1" .Values.s3.region -}}
{{- $s3Roles := list }}
{{- range .Values.s3.access }}
  {{- $role := dict "bucketName" .bucketName "acl" .acl }}
  {{- $s3Roles = append $s3Roles $role }}
{{- end }}
{{- $s3RolesJson := toJson $s3Roles | replace "\"" "\\\"" }}

{{- $scriptDebuggingEnabled := default "false" .Values.scriptDebuggingEnabled }}
{{- $namespace := .Release.Namespace -}}
{{- $multiDCServiceUnified := default "false" .Values.multiDCServiceUnified }}
{{- $hostnameSuffix := default ".strawmine.com" .Values.hostnameSuffix }}
{{- $adminHostnameSuffix := default (printf ".admin%s" $hostnameSuffix ) .Values.adminHostnameSuffix }}
{{- $serviceInternalEndpoint := printf "http://%s-%s.%s.svc.cluster.local" $env $serviceName $namespace }}

{{- $kafkaMessageMaxBytes := default "10485760" .Values.kafka.consumer.messageMaxBytes }}
{{- $isKafkaEnabled := .Values.kafka.enabled -}}

{{/*
Expects 
*/}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $env }}-{{ $serviceName }}-override-conf
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
data:
  override.conf: |
    include "{{ .Values.configurationFile}}"

    multidc_service_unified = {{ $multiDCServiceUnified }}
    multienv.prod_mode = {{ $envType | upper | quote }}
    multienv.server_env= {{ $env | quote }}
    multienv.hostname_suffix= {{ $hostnameSuffix | quote }}
    multienv.admin_hostname_suffix= {{ $adminHostnameSuffix | quote }}
    service_identifier= {{ $serviceName | quote }}
    service-url= {{ $serviceInternalEndpoint | quote }}

    # HARDCODED VALUES
    service-discovery.env="SEA_GLOBAL"
    service-discovery.v1.sdEnv="SEA_GLOBAL"

    # TODO: JWT tokens should be moved to secret 
    app.security.jwt.verification.secrets : ["123456789A123456789B123456789C12"]
    app.security.jwt.signature.salt = "123456789A123456789B123456789C12"
    {{ $envType }}.{{ $env }}.app.security.jwt.verification.secrets : ["123456789A123456789B123456789C12"]
    {{ $envType }}.{{ $env }}.app.security.jwt.signature.salt = "123456789A123456789B123456789C12"

    http.port = {{ .Values.service.targetPort }}
    node.ip = ${?POD_IP}
    node.name = ${?POD_NAME}

    include "/opt/service_cluster_context_v2.conf"
    include "/opt/service-confs/override-service-endpoints.conf"
    include "/opt/service-confs/override-service-external-confs.conf"
    # include "/opt/service-confs/override-application.conf"
 
    # Any override for service endpoints given using helm value overrideServiceEndpoints
    {{- range $key, $value := .Values.overrideServiceEndpoints }}
    {{ $key }}="{{ $value }}"
    {{- end }}

    # Any override for service config given using helm value overrideServiceConfs
    {{- range $key, $value := .Values.overrideServiceConfs }}
    {{ $key }}="{{ $value }}"
    {{- end }}

    {{- range $key, $value := .Values.overrideServiceConfsWithoutQuotes }}
    {{ $key }}={{ $value }}
    {{- end }}

    apps.druid.coordinator = "http://{{ .Values.druid.coordinator.serviceName }}:{{ .Values.druid.coordinator.port }}"
    {{ $envType }}.{{ $env }}.apps.druid.coordinator = "http://{{ .Values.druid.coordinator.serviceName }}:{{ .Values.druid.coordinator.port }}"
    
    apps.druid.broker = "http://{{ .Values.druid.broker.serviceName }}:{{ .Values.druid.broker.port }}"
    {{ $envType }}.{{ $env }}.apps.druid.broker = "http://{{ .Values.druid.broker.serviceName }}:{{ .Values.druid.broker.port }}"
    

    {{- if $isMongoEnabled }}
    {{ $envType }}.{{ $env }}.is_mongo_required = "true"
    include "/opt/service-confs/mongo-access-override.conf"
    {{- end }}
    {{- if $isPsqlEnabled }}
    # Contains the password for postgres
    include "/opt/service-confs/postgres-access-override.conf"
    # Contains other postgres configurations like database to connect db-pools etc.
    include "/opt/service-confs/postgres-override.conf"
    {{- end }}
    {{- if $isS3Enabled }}
    {{- if $isS3ObjectStorageInterfaceFactoryModuleEnabled }}
    play.modules.enabled += util.integrations.s3.modules.AWSS3ObjectStorageInterfaceFactoryModule
    {{- end }}
    include "/opt/service-confs/s3-access-override.conf"
    {{- end }}

    {{ $envType }}.{{ $env }}.instance_type_endpoints {
    Amazon EC2 : "http://dummy-server.stage-backend.svc.cluster.local"
    Microsoft Corporation : "http://169.254.169.254/metadata/instance/compute/vmSize?api-version=2017-08-01&format=text"
    Alibaba Cloud : "http://100.100.100.200/latest/meta-data/instance/instance-type"
    }
    {{- if $isKafkaEnabled }}
    {{ $envType }}.{{ $env }}.is_kafka_required = "true"
    {{ $envType }}.{{ $env }}.is_zookeeper_required = "true"
    apps.kafka="{{ .Values.kafka.masterEndpoint }}:{{ .Values.kafka.port }}"
    {{ $envType }}.{{ $env }}.apps.kafka="{{ .Values.kafka.masterEndpoint }}:{{ .Values.kafka.port }}"
    apps.kafka-rest = "http://{{ .Values.kafka.serviceName }}:{{ .Values.kafka.restPort }}"
    {{ $envType }}.{{ $env }}.apps.kafka-rest = "http://{{ .Values.kafka.masterEndpoint }}"
    {{ $envType }}.{{ $env }}.kafka-env = {{ $env }}

    apps.analytic-kafka = "{{ .Values.analyticKafka.masterEndpoint }}:{{ .Values.analyticKafka.port }}"
    apps.analytic-kafka-rest = "http://{{ .Values.analyticKafka.serviceName }}:{{ .Values.analyticKafka.restPort }}"
    {{ $envType }}.{{ $env }}.apps.analytic-kafka = "{{ .Values.analyticKafka.masterEndpoint }}:{{ .Values.analyticKafka.port }}"
    {{ $envType }}.{{ $env }}.apps.analytic-kafka-rest = "http://{{ .Values.analyticKafka.serviceName }}:{{ .Values.analyticKafka.restPort }}"
    {{ $envType }}.{{ $env }}.kafka-consumer-properties = "/opt/service-confs/kafka-consumer.properties"
    {{ $envType }}.{{ $env }}.kafka-producer-properties = "/opt/service-confs/kafka-producer.properties"
    {{ $envType }}.{{ $env }}.kafka-consumer-group.naming-strategy = "WITH_SD_ENV"
    #Including kafka.consumer property because thats how it is being read at lib
    #zkConnect is still being read from kafka-consumer.properties
    kafka.consumer.fetch.message.max.bytes={{ $kafkaMessageMaxBytes }}
    {{- end }}

    #Akka configs
    akka {
      stdout-loglevel = "ERROR" #doesn't seems to be working
      log-dead-letters-during-shutdown = off
      log-config-on-start = off #doesn't work for stdout
      loggers = ["akka.event.slf4j.Slf4jLogger"]
      logging-filter = "akka.event.slf4j.Slf4jLoggingFilter"
      loglevel = "WARNING"

    }
    {{- if .Values.overrideServiceConfSecret.name }}
    include "/opt/service-confs/{{ .Values.overrideServiceConfSecret.key }}"
    {{- end }}
