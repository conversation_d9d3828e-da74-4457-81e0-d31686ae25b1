{{- if and .Values.heapdump .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-pvc
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.heapdump.storage | default "5Gi" }}
  storageClassName: {{ .Values.heapdump.storageClassName | default "efs-sc" }}
  volumeName: {{ $qualifiedServiceName }}-heapdump-pv
{{- end }}
