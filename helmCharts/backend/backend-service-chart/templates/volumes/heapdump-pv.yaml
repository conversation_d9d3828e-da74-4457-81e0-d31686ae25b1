{{- if and .Values.heapdump .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-pv
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
spec:
  capacity:
    storage: {{ .Values.heapdump.storage | default "5Gi" }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ .Values.heapdump.storageClassName | default "efs-sc" }}
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ .Values.heapdump.efsFileSystemId | quote }}
    volumeAttributes:
      path: {{ .Values.heapdump.efsPath | default "/" | quote }}
{{- end }}