{{- $scriptDebuggingEnabled := (default "false" .Values.scriptDebuggingEnabled) | toString }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
{{- $javaOpts := "" -}}
{{- range $index, $agent := .Values.java.agents }}
{{- $javaOpts = printf "%s -javaagent:%s %s" $javaOpts $agent.path $agent.options | trim -}}
{{- end }}
{{- $javaOpts = printf "%s %s" $javaOpts .Values.java.additionalOpts | trim -}}
{{- $overrideConfConfigMap := printf "%s-%s-override-conf" $environment $serviceName }}
{{- $overrideApplicationConfConfigMap := printf "%s-%s-override-application-conf" $environment $serviceName }}
{{- $isMongoEnabled := .Values.mongo.enabled }}
{{- $isPsqlEnabled := .Values.postgres.enabled }}
{{- $isS3Enabled := .Values.s3.enabled }}
{{- $mongoSecretName := printf "%s-%s-mongo-pass" $environment $serviceName }}
{{- $psqlSecretName := printf "%s-%s-postgres-pass" $environment $serviceName }}
{{- $s3SecretName := printf "%s-%s-s3-credentials" $environment $serviceName }}
{{- $psqlConfigMapName := printf "%s-%s-postgres-configmap" $environment $serviceName }}

{{- $isKafkaEnabled := .Values.kafka.enabled }}
{{- $kafkaProducerConfigMapName := printf "%s-%s-kafka-producer.properties" $environment $serviceName }}
{{- $kafkaConsumerConfigMapName := printf "%s-%s-kafka-consumer.properties" $environment $serviceName }}
{{- $releaseName := .Release.Name }}
{{- $serviceClusterContextConfigMap := printf "%s-cluster-context" $qualifiedServiceName }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ $qualifiedServiceName }}
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
spec:

  serviceName: {{ $qualifiedServiceName }}-headless
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "backend-service-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "backend-service-chart.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
{{- if .Values.hostAliases }}
      hostAliases:
{{ toYaml .Values.hostAliases | indent 8 }}
{{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "backend-service-chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}

      containers:
        - name: backend
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            {{- range .Values.service.ports }}
            - containerPort: {{ .targetPort }}
              name: {{ .name }}
              protocol: TCP
            {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path }}
              port: {{ .Values.livenessProbe.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path }}
              port: {{ .Values.readinessProbe.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.resources }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
          env:
            - name: JAVA_OPTS
              value: "{{ $javaOpts }}"
            - name: APP_ARGS
              value: "-Dpidfile.path={{ .Values.app.pidfilePath }} -Dconfig.file={{ .Values.app.configFile }}  -Dservice_instance.node_id={{ .Values.app.serviceInstanceNodeId }} -Dstatus.report.started_by={{ .Values.app.statusReportStartedBy }} -Djava.io.tmpdir=/tmp/{{ $serviceName }}"
            - name: JVM_ARGS
              {{- if .Values.heapdump.enabled }}
              value: "-J-XX:+HeapDumpOnOutOfMemoryError -J-XX:HeapDumpPath=/opt/heapdumps/${POD_NAME} -J-XX:+ExitOnOutOfMemoryError"
              {{- else }}
              value: "-J-XX:+HeapDumpOnOutOfMemoryError -J-XX:HeapDumpPath={{ .Values.jvm.heapDumpPath }} -J-XX:+ExitOnOutOfMemoryError"
              {{- end }}
            - name: JVM_MEMORY_ARGS
              value: "-J-Xms{{ .Values.jvm.memory.Xms }} -J-Xmx{{ .Values.jvm.memory.Xmx }}"
            - name: DAEMON_ARGS
              value: "-- ${JVM_ARGS} ${JVM_MEMORY_ARGS} ${APP_ARGS}"
            - name: APPLICATION_NAME # Is this required??
              value: "{{ $serviceName }}"
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: QUALIFIED_SERVICE_NAME
              value: "{{ $qualifiedServiceName }}" 
            - name: SCRIPT_DEBUGGING_ENABLED
              value: "{{ $scriptDebuggingEnabled }}"

          volumeMounts:
            - name: scripts-configmap
              mountPath: /scripts
            - name: env-config-volume
              mountPath: /etc/additional_envs
            - name: override-configs
              mountPath: /opt/service-confs # This will mount all the projected volume content here
            {{- if .Values.heapdump.enabled }}
            - name: heapdump-volume
              mountPath: /opt/heapdumps
            {{- end }}
          {{- if .Values.volumeMounts }}
            {{- toYaml .Values.volumeMounts | nindent 12 }}
          {{- end }}
          command:
            - bash
            - -c
            - |
              mkdir -p {{ .Values.app.javaTmpDir }}
              chmod 777 {{ .Values.app.javaTmpDir }}
              {{- if .Values.heapdump.enabled }}
              mkdir -p /opt/heapdumps/${POD_NAME}
              chmod 777 /opt/heapdumps/${POD_NAME}
              {{- end }}
              bash /scripts/create_dynamic_environment_variables.sh               # Run the script to create environment variables
              source /etc/additional_envs/.env                  # Source the generated environment file
              echo "SERVICE_INSTANCE_ROLE is $SERVICE_INSTANCE_ROLE"
              echo "JAVA_OPTS is ${JAVA_OPTS}"
              echo "will run"
              executable=`ls /app/bin | grep -v bat`
              echo ${executable}
              echo "exec /app/bin/{{ $serviceName }} -Dservice_instance.role=${SERVICE_INSTANCE_ROLE} ${JVM_ARGS} ${JVM_MEMORY_ARGS} ${APP_ARGS}"
              # echo "sleeping now"
              # sleep 10000
              # exec java ${JAVA_OPTS} ${JVM_ARGS} ${JVM_MEMORY_ARGS} ${APP_ARGS} -jar /app/bin/${APPLICATION_NAME}
              # exec /app/bin/${APPLICATION_NAME} "-- ${JAVA_OPTS} ${JVM_ARGS} ${JVM_MEMORY_ARGS} ${APP_ARGS}"
              # exec /app/bin/{{ $serviceName }}   -J-XX:+HeapDumpOnOutOfMemoryError   -J-XX:HeapDumpPath=/opt/heapdumps   -J-XX:+ExitOnOutOfMemoryError   -J-Xms512m   -J-Xmx1024m   -Dpidfile.path=/dev/null   -Dconfig.file=/opt/service-confs/override.conf   -Dservice_instance.role="${SERVICE_INSTANCE_ROLE}"   -Dservice_instance.node_id=9999   -Dstatus.report.started_by=ops-user   -Djava.io.tmpdir={{ .Values.app.javaTmpDir }}
              {{- if .Values.heapdump.enabled }}
              exec /app/bin/${executable} -J-XX:+HeapDumpOnOutOfMemoryError   -J-XX:HeapDumpPath=/opt/heapdumps/${POD_NAME}   -J-XX:+ExitOnOutOfMemoryError   -J-Xms512m   -J-Xmx1024m   -Dpidfile.path=/dev/null   -Dconfig.file=/opt/service-confs/override.conf   -Dservice_instance.role="${SERVICE_INSTANCE_ROLE}"   -Dservice_instance.node_id=9999   -Dstatus.report.started_by=ops-user   -Djava.io.tmpdir={{ .Values.app.javaTmpDir }}
              {{- else }}
              exec /app/bin/${executable} -J-XX:+HeapDumpOnOutOfMemoryError   -J-XX:HeapDumpPath=/opt/heapdumps   -J-XX:+ExitOnOutOfMemoryError   -J-Xms512m   -J-Xmx1024m   -Dpidfile.path=/dev/null   -Dconfig.file=/opt/service-confs/override.conf   -Dservice_instance.role="${SERVICE_INSTANCE_ROLE}"   -Dservice_instance.node_id=9999   -Dstatus.report.started_by=ops-user   -Djava.io.tmpdir={{ .Values.app.javaTmpDir }}
              {{- end }}
      volumes:
        - name: scripts-configmap # Reference to the combined ConfigMap
          configMap:
            name: backend-scripts-configmap
        - name: env-config-volume
          emptyDir: {}
        {{- if .Values.heapdump.enabled }}
        - name: heapdump-volume
          persistentVolumeClaim:
            claimName: {{ printf "%s-%s" .Values.environment .Values.serviceName }}-heapdump-pvc
        {{- end }}
        - name: override-configs
          projected:
            sources:
            - configMap:
                name: {{ $overrideConfConfigMap }}
                items:
                  - key: override.conf
                    path: override.conf
            # - configMap:
            #     name: {{ $overrideApplicationConfConfigMap }}
            #     items:
            #       - key: override-application.conf
            #         path: override-application.conf
            - configMap:
                name: override-service-endpoints
                items:
                  - key: override-service-endpoints
                    path: override-service-endpoints.conf
            - configMap:
                name: override-service-endpoints-external
                items:
                  - key: override-service-endpoints-external
                    path: "override-service-external-confs.conf"
            - configMap:
                name: {{ $serviceClusterContextConfigMap }}
            {{- if $isMongoEnabled }}
            - secret:
                name: {{ $mongoSecretName }}
                items:
                  - key: mongo-access-override.conf
                    path: mongo-access-override.conf
            {{- end }}
            {{- if $isPsqlEnabled }}
            - secret:
                name: {{ $psqlSecretName }}
                items:
                  - key: postgres-access-override.conf
                    path: postgres-access-override.conf
            - configMap:
                name: {{ $psqlConfigMapName }}
                items:
                  - key: postgres-override.conf
                    path: postgres-override.conf
            {{- end }}

            {{- if $isKafkaEnabled }}
            - configMap:
                name: {{ $kafkaProducerConfigMapName }}
                items:
                  - key: kafka-producer.properties
                    path: kafka-producer.properties
            - configMap:
                name: {{ $kafkaConsumerConfigMapName }}
                items:
                  - key: kafka-consumer.properties
                    path: kafka-consumer.properties
            {{- end }}

            {{- if $isS3Enabled }}
            - secret:
                name: {{ $s3SecretName }}
                items:
                  - key: s3-access-override.conf
                    path: s3-access-override.conf
            {{- end }}
            - configMap:
                name: jmx-prometheus-config-configmap
            {{- if .Values.overrideServiceConfSecret.name }}
            - secret:
                name: {{ .Values.overrideServiceConfSecret.name }}
                items:
                  - key: {{ .Values.overrideServiceConfSecret.key }}
                    path: {{ .Values.overrideServiceConfSecret.key }}
            {{- end }}
{{/*
TODO: Add override Configmap and Secrets if not empty
*/}}

  {{- if .Values.createPVC }}
  volumeClaimTemplates:
    {{- toYaml .Values.volumeClaim | nindent 8 }}
  {{- end }}

