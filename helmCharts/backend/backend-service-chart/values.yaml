# Default values for backend-service-chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

#REQUIRED
# Sets the service name - MUST be overridden in service-specific values
serviceName: ""

# For devops: set to true for debugging the init job 
scriptDebuggingEnabled: false

#REQUIRED
# Sets the environment
environment: "kalpha" 

#REQUIRED
# prod/stage # Used for creating the service conf
envType: "stage" 

# REQUIRED
# can be prod.conf or prod_conf.conf for few services, check chef before deploy
configurationFile: "/app/conf/application.conf"

# This is used for configuration value multienv.hostname_suffix and multienv.admin_hostname_suffix
hostnameSuffix: ".strawmine.com" # Default is .strawmine.com

# Host aliases - can be overridden per service
hostAliases: []

# Service endpoint overrides - service specific
overrideServiceEndpoints: {}

# Service configuration overrides - service specific  
overrideServiceConfs: {}

# Override service configuration secret
overrideServiceConfSecret:
  name: ""
  key: ""

# Database configurations
postgres:
  enabled: false
  username: ""  # Defaults to <qualifiedServiceName> replacing hyphen with underscore
  # password: ""  # Generated by helm and stored in secret
  dbName: ""    # Defaults to the postgres.username
  port: "5432" # Port at which the service listens to
  cluster: ""
  dbConnectionpools:
    - name: "default"
      maxConnections: "10"
      minConnections: "5"
      numThreads: "10"
      connectionTimeout: "4000"
      registerMbeans: true

mongo:
  enabled: false
  mongoUri: ""
  external:
    enabled: false
    ip: ""
    port: "27017"
  username: ""  # Defaults to <environment>-<serviceName> replacing hyphen with underscore
  # password: "" # To be generated
  authenticationDatabase: "admin" # Defaults to NSP_Users

# Kafka configuration
kafka:
  enabled: false
  restEnabled: false
  serviceName: "stage-nalpha-kafka"
  namespace: "stage-kafka"
  port: 9093
  restPort: 8080
  zookeeperPort: 2181
  masterEndpoint: "************"
  producer:
    acks: -1    #Adding Acknowledgement from all insync replicas
    producerType: "sync"     # specifies whether the messages are sent asynchronously (async) or synchronously (sync)
    messageMaxBytes: "********"
    maxRequestSize: "********"
  consumer:
    maxPollIntervalMs: "*********" # Defaults to 10 days
    messageMaxBytes: "********"
  brokerEndpoints:
    - "************"

# S3 configuration
s3:
  enabled: false
  s3ObjectStorageInterfaceFactoryModuleEnabled: false
  region: "" # Defaults to ap-southeast-1
  access: [] # List of all the buckets for which access is required

# Init job configuration
initJob:
  enabled: false
  serviceAccount: "backend-service-chart-init-job-sa"

# Default replica count
replicaCount: 1

# Image configuration - MUST be overridden in service-specific values
image:
  repository: ""
  tag: ""
  pullPolicy: IfNotPresent

# Service configuration
service:
  type: ClusterIP
  ports:
    - name: http
      port: 80
      targetPort: 9000
      protocol: TCP
    - name: jmx-port
      port: 9050
      targetPort: 9050
      protocol: TCP

# Ingress configuration
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts: []
  tls: []

# Probes configuration
livenessProbe:
  enabled: true
  path: "/health"
  port: 9000
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

readinessProbe:
  enabled: true
  path: "/health"
  port: 9000
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  successThreshold: 1
  failureThreshold: 3

# Resource limits and requests
resources: {}

# Pod annotations and labels
podAnnotations: {}
podLabels: {}

# Security contexts
podSecurityContext: {}
securityContext: {}

# Application-specific arguments (APP_ARGS)
app:
  pidfilePath: "/dev/null"
  configFile: "/opt/service-confs/override.conf"
  serviceInstanceNodeId: "9999"
  statusReportStartedBy: "ops-user"
  javaTmpDir: "/tmp"  # Will be overridden per service

# JVM-specific arguments (JVM_ARGS)
jvm:
  heapDumpPath: "/opt/heapdumps"
  memory:
    Xms: "256m"
    Xmx: "256m"

# Additional volumeMounts on the output Deployment definition
volumeMounts: []

# Heapdump volume mount configuration (PVC managed by microservice-commons-chart)
heapdump:
  enabled: false  # Set to true to mount the shared heapdump volume

# Multi-DC service configuration
multiDCServiceUnified: false

# Druid configuration
druid:
  namespace: "stage-druid-cluster"
  coordinator:
    enabled: false
    port: 8081
    serviceName: "stage-nalpha-druid-coordinator"
    endpoints: []
  broker:
    enabled: false
    port: 8082
    serviceName: "stage-nalpha-druid-broker"
    endpoints: []

# Elasticsearch configuration
elasticsearch:
  enabled: false
  namespace: "stage-elasticsearch-cluster"
  serviceName: "elasticsearch-3s"
  port: 9200
  external: 
    enabled: false
    endpoints: []
    IPs: []

# Redis configuration
redis:
  cluster: ""
  port: ""

# Service account configuration
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

# Autoscaling configuration
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

# PVC configuration
createPVC: false
volumeClaim: []

# Image pull secrets
imagePullSecrets: []

# Node selector, tolerations, and affinity
nodeSelector: {}
tolerations: []
affinity: {}

# Java agents configuration
java:
  agents: []
  additionalOpts: ""
