#!/bin/bash

# Setup script for heapdump S3 bucket and IAM role
# This script creates the necessary AWS resources for the heapdump cleanup job

set -e

# Configuration
BUCKET_NAME="stage-eks-heapdumps"
REGION="ap-southeast-1"
CLUSTER_NAME="staging-solvei8-eks"  # Update with your cluster name
NAMESPACE="stage-backend"
SERVICE_ACCOUNT_NAME="backend-service-chart-heapdump-cleanup"
ROLE_NAME="EKS-HeapdumpCleanup-Role"
POLICY_NAME="EKS-HeapdumpCleanup-Policy"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed. Please install it first."
    exit 1
fi

# Get AWS account ID
ACCOUNT_ID=$(aws sts get-caller-identity --query "Account" --output text)
if [ -z "$ACCOUNT_ID" ]; then
    print_error "Failed to get AWS account ID. Please check your AWS credentials."
    exit 1
fi

print_info "AWS Account ID: $ACCOUNT_ID"
print_info "Region: $REGION"
print_info "Cluster: $CLUSTER_NAME"

# Create S3 bucket if it doesn't exist
print_info "Creating S3 bucket: $BUCKET_NAME"
if aws s3 ls "s3://$BUCKET_NAME" 2>/dev/null; then
    print_warning "S3 bucket $BUCKET_NAME already exists"
else
    if [ "$REGION" = "ap-southeast-1" ]; then
        aws s3 mb "s3://$BUCKET_NAME"
    else
        aws s3 mb "s3://$BUCKET_NAME" --region "$REGION"
    fi
    print_info "S3 bucket $BUCKET_NAME created successfully"
fi

# Enable versioning on the bucket
print_info "Enabling versioning on S3 bucket"
aws s3api put-bucket-versioning \
    --bucket "$BUCKET_NAME" \
    --versioning-configuration Status=Enabled

# Set lifecycle policy to delete old versions after 30 days
print_info "Setting lifecycle policy on S3 bucket"
cat > /tmp/lifecycle-policy.json << EOF
{
    "Rules": [
        {
            "ID": "HeapdumpCleanup",
            "Status": "Enabled",
            "Filter": {},
            "NoncurrentVersionExpiration": {
                "NoncurrentDays": 30
            },
            "AbortIncompleteMultipartUpload": {
                "DaysAfterInitiation": 7
            }
        }
    ]
}
EOF

aws s3api put-bucket-lifecycle-configuration \
    --bucket "$BUCKET_NAME" \
    --lifecycle-configuration file:///tmp/lifecycle-policy.json

# Create IAM policy
print_info "Creating IAM policy: $POLICY_NAME"
POLICY_ARN="arn:aws:iam::$ACCOUNT_ID:policy/$POLICY_NAME"

if aws iam get-policy --policy-arn "$POLICY_ARN" 2>/dev/null; then
    print_warning "IAM policy $POLICY_NAME already exists"
else
    aws iam create-policy \
        --policy-name "$POLICY_NAME" \
        --policy-document file://helmCharts/backend/backend-service-chart/templates/jobs/heapdump-s3-iam-policy.json \
        --description "Policy for EKS heapdump cleanup job to access S3"
    print_info "IAM policy $POLICY_NAME created successfully"
fi

# Get OIDC issuer URL
print_info "Getting OIDC issuer URL for cluster: $CLUSTER_NAME"
OIDC_ISSUER=$(aws eks describe-cluster --name "$CLUSTER_NAME" --query "cluster.identity.oidc.issuer" --output text)
if [ -z "$OIDC_ISSUER" ]; then
    print_error "Failed to get OIDC issuer URL for cluster $CLUSTER_NAME"
    exit 1
fi

OIDC_ISSUER_HOSTNAME=$(echo "$OIDC_ISSUER" | sed 's|https://||')
print_info "OIDC Issuer: $OIDC_ISSUER_HOSTNAME"

# Create trust policy for the IAM role
cat > /tmp/trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::$ACCOUNT_ID:oidc-provider/$OIDC_ISSUER_HOSTNAME"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "$OIDC_ISSUER_HOSTNAME:sub": "system:serviceaccount:$NAMESPACE:$SERVICE_ACCOUNT_NAME",
                    "$OIDC_ISSUER_HOSTNAME:aud": "sts.amazonaws.com"
                }
            }
        }
    ]
}
EOF

# Create IAM role
print_info "Creating IAM role: $ROLE_NAME"
ROLE_ARN="arn:aws:iam::$ACCOUNT_ID:role/$ROLE_NAME"

if aws iam get-role --role-name "$ROLE_NAME" 2>/dev/null; then
    print_warning "IAM role $ROLE_NAME already exists"
else
    aws iam create-role \
        --role-name "$ROLE_NAME" \
        --assume-role-policy-document file:///tmp/trust-policy.json \
        --description "Role for EKS heapdump cleanup job"
    print_info "IAM role $ROLE_NAME created successfully"
fi

# Attach policy to role
print_info "Attaching policy to role"
aws iam attach-role-policy \
    --role-name "$ROLE_NAME" \
    --policy-arn "$POLICY_ARN"

# Clean up temporary files
rm -f /tmp/trust-policy.json /tmp/lifecycle-policy.json

print_info "Setup completed successfully!"
print_info ""
print_info "Next steps:"
print_info "1. Update your values files with the following configuration:"
print_info "   heapdump:"
print_info "     serviceAccount:"
print_info "       roleArn: \"$ROLE_ARN\""
print_info ""
print_info "2. Deploy your services using the updated values"
print_info ""
print_info "3. The CronJob will run every 15 minutes to check for heapdump files"
print_info "   and upload them to s3://$BUCKET_NAME/<pod-name>/"
