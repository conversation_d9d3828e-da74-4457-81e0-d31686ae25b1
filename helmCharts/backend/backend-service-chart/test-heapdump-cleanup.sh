#!/bin/bash

# Test script for heapdump cleanup job
# This script creates test heap dump files and verifies the cleanup job works

set -e

# Configuration
NAMESPACE="stage-backend"
SERVICE_NAME="n-authentication"  # Change this to test different services
ENVIRONMENT="kalpha"
QUALIFIED_SERVICE_NAME="${ENVIRONMENT}-${SERVICE_NAME}"
POD_NAME="${QUALIFIED_SERVICE_NAME}-0"  # Assuming first pod
S3_BUCKET="stage-eks-heapdumps"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed"
    exit 1
fi

# Check if aws cli is available
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed"
    exit 1
fi

print_info "Testing heapdump cleanup for service: $QUALIFIED_SERVICE_NAME"
print_info "Namespace: $NAMESPACE"
print_info "S3 Bucket: $S3_BUCKET"

# Check if the pod exists
if ! kubectl get pod "$POD_NAME" -n "$NAMESPACE" &>/dev/null; then
    print_error "Pod $POD_NAME not found in namespace $NAMESPACE"
    print_info "Available pods:"
    kubectl get pods -n "$NAMESPACE" | grep "$QUALIFIED_SERVICE_NAME" || echo "No pods found"
    exit 1
fi

print_info "Found pod: $POD_NAME"

# Create a test heap dump file
TEST_FILE="test_heapdump_$(date +%s).hprof"
TEST_CONTENT="This is a test heap dump file created at $(date)"

print_info "Creating test heap dump file: $TEST_FILE"
kubectl exec "$POD_NAME" -n "$NAMESPACE" -- bash -c "
    mkdir -p /opt/heapdumps/\$HOSTNAME
    echo '$TEST_CONTENT' > /opt/heapdumps/\$HOSTNAME/$TEST_FILE
    ls -la /opt/heapdumps/\$HOSTNAME/
"

print_info "Test file created successfully"

# Check if CronJob exists
CRONJOB_NAME="${QUALIFIED_SERVICE_NAME}-heapdump-cleanup"
if ! kubectl get cronjob "$CRONJOB_NAME" -n "$NAMESPACE" &>/dev/null; then
    print_error "CronJob $CRONJOB_NAME not found in namespace $NAMESPACE"
    print_info "Available CronJobs:"
    kubectl get cronjobs -n "$NAMESPACE"
    exit 1
fi

print_info "Found CronJob: $CRONJOB_NAME"

# Create a manual job from the CronJob
MANUAL_JOB_NAME="${CRONJOB_NAME}-manual-test-$(date +%s)"
print_info "Creating manual job: $MANUAL_JOB_NAME"

kubectl create job --from=cronjob/"$CRONJOB_NAME" "$MANUAL_JOB_NAME" -n "$NAMESPACE"

# Wait for the job to complete
print_info "Waiting for job to complete..."
kubectl wait --for=condition=complete job/"$MANUAL_JOB_NAME" -n "$NAMESPACE" --timeout=300s

# Check job status
JOB_STATUS=$(kubectl get job "$MANUAL_JOB_NAME" -n "$NAMESPACE" -o jsonpath='{.status.conditions[0].type}')
if [ "$JOB_STATUS" = "Complete" ]; then
    print_info "Job completed successfully"
else
    print_error "Job failed or did not complete"
    kubectl describe job "$MANUAL_JOB_NAME" -n "$NAMESPACE"
    exit 1
fi

# Show job logs
print_info "Job logs:"
kubectl logs job/"$MANUAL_JOB_NAME" -n "$NAMESPACE"

# Check if file was uploaded to S3
print_info "Checking S3 for uploaded file..."
sleep 5  # Give S3 a moment to be consistent

if aws s3 ls "s3://$S3_BUCKET/$POD_NAME/$TEST_FILE" &>/dev/null; then
    print_info "✅ Test file found in S3: s3://$S3_BUCKET/$POD_NAME/$TEST_FILE"
    
    # Download and verify content
    TEMP_FILE="/tmp/$TEST_FILE"
    aws s3 cp "s3://$S3_BUCKET/$POD_NAME/$TEST_FILE" "$TEMP_FILE"
    
    if grep -q "This is a test heap dump file" "$TEMP_FILE"; then
        print_info "✅ File content verified in S3"
    else
        print_warning "⚠️  File content mismatch in S3"
    fi
    
    rm -f "$TEMP_FILE"
else
    print_error "❌ Test file not found in S3"
fi

# Check if local file was removed
print_info "Checking if local file was removed..."
if kubectl exec "$POD_NAME" -n "$NAMESPACE" -- test -f "/opt/heapdumps/\$HOSTNAME/$TEST_FILE" 2>/dev/null; then
    print_warning "⚠️  Local file still exists (this might be expected if S3 upload failed)"
    kubectl exec "$POD_NAME" -n "$NAMESPACE" -- ls -la "/opt/heapdumps/\$HOSTNAME/"
else
    print_info "✅ Local file was removed successfully"
fi

# Clean up the manual job
print_info "Cleaning up manual job..."
kubectl delete job "$MANUAL_JOB_NAME" -n "$NAMESPACE"

# Clean up S3 test file
print_info "Cleaning up S3 test file..."
aws s3 rm "s3://$S3_BUCKET/$POD_NAME/$TEST_FILE" || print_warning "Failed to remove S3 test file"

print_info "Test completed!"
print_info ""
print_info "Summary:"
print_info "- Created test heap dump file in pod"
print_info "- Triggered manual cleanup job"
print_info "- Verified file upload to S3"
print_info "- Verified local file cleanup"
print_info "- Cleaned up test resources"
