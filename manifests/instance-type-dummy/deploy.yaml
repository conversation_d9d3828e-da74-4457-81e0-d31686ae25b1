apiVersion: apps/v1
kind: Deployment
metadata:
  name: dummy-server
  namespace: stage-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dummy-server
  template:
    metadata:
      labels:
        app: dummy-server
    spec:
      containers:
      - name: dummy-server
        image: hashicorp/http-echo:0.2.3
        args:
          - "-listen=:8080"
          - "-text=dummy"
        ports:
        - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: dummy-server
  namespace: stage-backend
spec:
  selector:
    app: dummy-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
